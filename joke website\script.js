document.addEventListener('DOMContentLoaded', function() {
    // DOM Elements
    const jokeButton = document.getElementById('joke-button');
    const jokeDisplay = document.getElementById('joke-display');
    const emojiReaction = document.getElementById('emoji-reaction');
    const saveButton = document.getElementById('save-button');
    const savedJokesList = document.getElementById('saved-jokes-list');
    const themeToggle = document.getElementById('theme-toggle');
    
    // State
    let currentJoke = null;
    let savedJokes = JSON.parse(localStorage.getItem('savedJokes')) || [];
    
    // Initialize
    updateSavedJokesList();
    
    // Event Listeners
    jokeButton.addEventListener('click', fetchJoke);
    saveButton.addEventListener('click', saveCurrentJoke);
    themeToggle.addEventListener('click', toggleTheme);
    
    // Functions
    async function fetchJoke() {
        try {
            // Show loading state
            jokeButton.disabled = true;
            jokeButton.querySelector('.button-text').textContent = 'Loading...';
            
            const response = await fetch('https://v2.jokeapi.dev/joke/Any?safe-mode');
            const data = await response.json();
            
            currentJoke = data;
            displayJoke(data);
            showReaction(data);
            
            // Reset button
            jokeButton.disabled = false;
            jokeButton.querySelector('.button-text').textContent = 'Tell Me a Joke';
        } catch (error) {
            console.error('Error fetching joke:', error);
            jokeDisplay.innerHTML = '<p class="error">Oops! Could not load a joke. Try again later.</p>';
            
            // Reset button
            jokeButton.disabled = false;
            jokeButton.querySelector('.button-text').textContent = 'Try Again';
        }
    }
    
    function displayJoke(joke) {
        jokeDisplay.innerHTML = '';
        jokeDisplay.classList.remove('fade-in');
        
        if (joke.type === 'single') {
            const jokeElement = document.createElement('p');
            jokeElement.textContent = joke.joke;
            jokeDisplay.appendChild(jokeElement);
        } else if (joke.type === 'twopart') {
            const setupElement = document.createElement('p');
            setupElement.textContent = joke.setup;
            
            const deliveryElement = document.createElement('p');
            deliveryElement.textContent = joke.delivery;
            deliveryElement.style.marginTop = '1rem';
            deliveryElement.style.fontWeight = '600';
            deliveryElement.style.color = getRandomColor();
            
            jokeDisplay.appendChild(setupElement);
            jokeDisplay.appendChild(deliveryElement);
        }
        
        // Trigger animation
        setTimeout(() => {
            jokeDisplay.classList.add('fade-in');
        }, 10);
    }
    
    function showReaction(joke) {
        // Clear previous reaction
        emojiReaction.innerHTML = '';
        emojiReaction.classList.remove('emoji-pop');
        
        // Determine appropriate emoji based on joke category
        let emoji = '😂'; // default
        
        if (joke.category === 'Programming') {
            emoji = '👨‍💻';
        } else if (joke.category === 'Dark') {
            emoji = '😈';
        } else if (joke.category === 'Pun') {
            emoji = '🎭';
        } else if (joke.category === 'Spooky') {
            emoji = '👻';
        } else if (joke.category === 'Christmas') {
            emoji = '🎄';
        }
        
        // Create emoji element
        const emojiElement = document.createElement('span');
        emojiElement.textContent = emoji;
        emojiReaction.appendChild(emojiElement);
        
        // Trigger animation
        setTimeout(() => {
            emojiReaction.classList.add('emoji-pop');
        }, 10);
    }
    
    function saveCurrentJoke() {
        if (!currentJoke) return;
        
        // Check if joke is already saved
        const isAlreadySaved = savedJokes.some(savedJoke => {
            if (savedJoke.type === 'single' && currentJoke.type === 'single') {
                return savedJoke.joke === currentJoke.joke;
            } else if (savedJoke.type === 'twopart' && currentJoke.type === 'twopart') {
                return savedJoke.setup === currentJoke.setup && savedJoke.delivery === currentJoke.delivery;
            }
            return false;
        });
        
        if (isAlreadySaved) {
            alert('This joke is already saved!');
            return;
        }
        
        // Add to saved jokes
        savedJokes.push(currentJoke);
        localStorage.setItem('savedJokes', JSON.stringify(savedJokes));
        updateSavedJokesList();
        
        // Show confirmation
        const confirmation = document.createElement('span');
        confirmation.textContent = 'Saved!';
        confirmation.style.color = '#4CAF50';
        confirmation.style.marginLeft = '0.5rem';
        saveButton.appendChild(confirmation);
        
        setTimeout(() => {
            confirmation.remove();
        }, 2000);
    }
    
    function updateSavedJokesList() {
        savedJokesList.innerHTML = '';
        
        if (savedJokes.length === 0) {
            const emptyMessage = document.createElement('li');
            emptyMessage.textContent = 'No saved jokes yet. Save some to see them here!';
            emptyMessage.style.color = 'var(--text-secondary)';
            emptyMessage.style.fontStyle = 'italic';
            savedJokesList.appendChild(emptyMessage);
            return;
        }
        
        savedJokes.forEach((joke, index) => {
            const listItem = document.createElement('li');
            
            if (joke.type === 'single') {
                listItem.textContent = joke.joke;
            } else if (joke.type === 'twopart') {
                listItem.innerHTML = `
                    <p>${joke.setup}</p>
                    <p style="font-weight: 600; color: ${getRandomColor()}">${joke.delivery}</p>
                `;
            }
            
            // Add delete button
            const deleteButton = document.createElement('button');
            deleteButton.textContent = '❌';
            deleteButton.style.marginLeft = '0.5rem';
            deleteButton.style.background = 'none';
            deleteButton.style.border = 'none';
            deleteButton.style.cursor = 'pointer';
            deleteButton.addEventListener('click', () => deleteSavedJoke(index));
            
            listItem.appendChild(deleteButton);
            savedJokesList.appendChild(listItem);
        });
    }
    
    function deleteSavedJoke(index) {
        savedJokes.splice(index, 1);
        localStorage.setItem('savedJokes', JSON.stringify(savedJokes));
        updateSavedJokesList();
    }
    
    function toggleTheme() {
        const currentTheme = document.documentElement.getAttribute('data-theme');
        const newTheme = currentTheme === 'light' ? 'dark' : 'light';
        
        document.documentElement.setAttribute('data-theme', newTheme);
        
        // Update icon
        const icon = themeToggle.querySelector('i');
        icon.className = newTheme === 'light' ? 'fas fa-sun' : 'fas fa-moon';
        
        // Save preference
        localStorage.setItem('theme', newTheme);
    }
    
    function getRandomColor() {
        const colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#FFA07A', '#98D8C8', '#F06292'];
        return colors[Math.floor(Math.random() * colors.length)];
    }
    
    // Check for saved theme preference
    const savedTheme = localStorage.getItem('theme');
    if (savedTheme) {
        document.documentElement.setAttribute('data-theme', savedTheme);
        const icon = themeToggle.querySelector('i');
        icon.className = savedTheme === 'light' ? 'fas fa-sun' : 'fas fa-moon';
    }
});