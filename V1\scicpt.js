document.addEventListener('DOMContentLoaded', () => {
    const cityInput = document.getElementById('city-input');
    const searchBtn = document.getElementById('search-btn');
    const errorMessage = document.getElementById('error-message');
    const weatherContainer = document.getElementById('weather-container');
    const cityName = document.getElementById('city-name');
    const temperature = document.getElementById('temperature');
    const weatherDesc = document.getElementById('weather-desc');
    const humidity = document.getElementById('humidity');
    const windSpeed = document.getElementById('wind-speed');

    searchBtn.addEventListener('click', fetchWeather);
    cityInput.addEventListener('keypress', (e) => {
        if (e.key === 'Enter') {
            fetchWeather();
        }
    });

    function fetchWeather() {
        const city = cityInput.value.trim();
        
        if (!city) {
            showError('Please enter a city name');
            return;
        }

        fetch(`https://wttr.in/${city}?format=j1`)
            .then(response => {
                if (!response.ok) {
                    throw new Error('City not found');
                }
                return response.json();
            })
            .then(data => {
                displayWeather(data);
            })
            .catch(error => {
                showError('City not found. Please try another location.');
                console.error('Error fetching weather data:', error);
            });
    }

    function displayWeather(data) {
        errorMessage.textContent = '';
        
        const currentCondition = data.current_condition[0];
        const area = data.nearest_area[0];
        
        cityName.textContent = `${area.areaName[0].value}, ${area.region[0].value}, ${area.country[0].value}`;
        temperature.textContent = `${currentCondition.temp_C}°C`;
        weatherDesc.textContent = currentCondition.weatherDesc[0].value;
        humidity.textContent = `${currentCondition.humidity}%`;
        windSpeed.textContent = `${currentCondition.windspeedKmph} kmph`;
        
        weatherContainer.classList.remove('hidden');
    }

    function showError(message) {
        errorMessage.textContent = message;
        weatherContainer.classList.add('hidden');
    }
});