<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Register - Smokey AI</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'inter': ['Inter', 'sans-serif'],
                    },
                    colors: {
                        'primary': '#8b5cf6',
                        'primary-dark': '#7c3aed',
                        'dark-bg': '#0f172a',
                        'dark-surface': '#1e293b',
                        'dark-border': '#334155',
                    }
                }
            }
        }
    </script>
    <style>
        body {
            font-family: 'Inter', sans-serif;
        }
        
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        .glass-effect {
            backdrop-filter: blur(20px);
            background: rgba(30, 41, 59, 0.8);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .input-glow:focus {
            box-shadow: 0 0 0 3px rgba(139, 92, 246, 0.3);
        }
    </style>
</head>
<body class="min-h-screen gradient-bg flex items-center justify-center p-4">
    <div class="relative z-10 w-full max-w-md">
        <!-- Logo and Title -->
        <div class="text-center mb-8">
            <div class="inline-flex items-center justify-center w-16 h-16 bg-white bg-opacity-20 rounded-2xl mb-4 glass-effect">
                <span class="text-3xl">🐾</span>
            </div>
            <h1 class="text-3xl font-bold text-white mb-2">Join Smokey AI</h1>
            <p class="text-gray-300">Create your account and start chatting!</p>
        </div>

        <!-- Registration Form -->
        <div class="glass-effect rounded-2xl p-8 shadow-2xl">
            <form id="registerForm" class="space-y-6">
                <!-- Username Field -->
                <div>
                    <label for="username" class="block text-sm font-medium text-gray-300 mb-2">Username</label>
                    <input 
                        type="text" 
                        id="username" 
                        placeholder="Choose a unique username"
                        class="w-full px-4 py-3 bg-dark-surface border border-dark-border rounded-xl text-white placeholder-gray-400 focus:outline-none focus:border-primary input-glow transition-all duration-200"
                        required
                    >
                    <div id="usernameError" class="text-red-400 text-sm mt-1 hidden"></div>
                </div>

                <!-- Email Field -->
                <div>
                    <label for="email" class="block text-sm font-medium text-gray-300 mb-2">Email</label>
                    <input 
                        type="email" 
                        id="email" 
                        placeholder="<EMAIL>"
                        class="w-full px-4 py-3 bg-dark-surface border border-dark-border rounded-xl text-white placeholder-gray-400 focus:outline-none focus:border-primary input-glow transition-all duration-200"
                        required
                    >
                    <div id="emailError" class="text-red-400 text-sm mt-1 hidden"></div>
                </div>

                <!-- Password Field -->
                <div>
                    <label for="password" class="block text-sm font-medium text-gray-300 mb-2">Password</label>
                    <input 
                        type="password" 
                        id="password" 
                        placeholder="Create a strong password"
                        class="w-full px-4 py-3 bg-dark-surface border border-dark-border rounded-xl text-white placeholder-gray-400 focus:outline-none focus:border-primary input-glow transition-all duration-200"
                        required
                    >
                    <div id="passwordError" class="text-red-400 text-sm mt-1 hidden"></div>
                </div>

                <!-- Confirm Password Field -->
                <div>
                    <label for="confirmPassword" class="block text-sm font-medium text-gray-300 mb-2">Confirm Password</label>
                    <input 
                        type="password" 
                        id="confirmPassword" 
                        placeholder="Confirm your password"
                        class="w-full px-4 py-3 bg-dark-surface border border-dark-border rounded-xl text-white placeholder-gray-400 focus:outline-none focus:border-primary input-glow transition-all duration-200"
                        required
                    >
                    <div id="confirmPasswordError" class="text-red-400 text-sm mt-1 hidden"></div>
                </div>

                <!-- Terms and Conditions -->
                <div class="flex items-start">
                    <input type="checkbox" id="terms" class="w-4 h-4 text-primary bg-dark-surface border-dark-border rounded focus:ring-primary focus:ring-2 mt-1">
                    <label for="terms" class="ml-2 text-sm text-gray-300">
                        I agree to the Terms of Service and Privacy Policy
                    </label>
                </div>
                <div id="termsError" class="text-red-400 text-sm hidden"></div>

                <!-- Register Button -->
                <button 
                    type="submit" 
                    id="registerBtn"
                    class="w-full bg-gradient-to-r from-primary to-primary-dark text-white font-semibold py-3 px-4 rounded-xl hover:from-primary-dark hover:to-purple-800 focus:outline-none focus:ring-2 focus:ring-primary transition-all duration-200"
                >
                    <span id="registerText">CREATE ACCOUNT</span>
                    <span id="registerSpinner" class="hidden">Creating account...</span>
                </button>
            </form>

            <!-- Login Link -->
            <div class="mt-6 text-center">
                <p class="text-gray-400">
                    Already have an account? 
                    <a href="login.html" class="text-primary hover:text-primary-dark font-medium transition-colors">
                        Sign in here
                    </a>
                </p>
            </div>
        </div>
    </div>

    <!-- Toast Notification -->
    <div id="toast" class="fixed top-4 right-4 z-50 hidden">
        <div id="toastContent" class="bg-green-500 text-white px-6 py-3 rounded-lg shadow-lg">
            <span id="toastMessage">Success!</span>
        </div>
    </div>

    <script>
        console.log('🚀 Registration page loaded');

        // Simple and reliable registration system
        document.getElementById('registerForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            console.log('📝 Form submitted');

            // Get form values
            const username = document.getElementById('username').value.trim();
            const email = document.getElementById('email').value.trim();
            const password = document.getElementById('password').value;
            const confirmPassword = document.getElementById('confirmPassword').value;
            const terms = document.getElementById('terms').checked;

            console.log('📋 Form data:', { username, email, hasPassword: !!password, terms });

            // Clear previous errors
            clearAllErrors();

            // Validate
            let isValid = true;

            if (!username || username.length < 3) {
                showError('username', 'Username must be at least 3 characters');
                isValid = false;
            }

            if (!email || !email.includes('@')) {
                showError('email', 'Please enter a valid email address');
                isValid = false;
            }

            if (!password || password.length < 6) {
                showError('password', 'Password must be at least 6 characters');
                isValid = false;
            }

            if (password !== confirmPassword) {
                showError('confirmPassword', 'Passwords do not match');
                isValid = false;
            }

            if (!terms) {
                showError('terms', 'You must accept the terms and conditions');
                isValid = false;
            }

            if (!isValid) {
                console.log('❌ Validation failed');
                showToast('Please fix the errors above', 'error');
                return;
            }

            console.log('✅ Validation passed');

            // Show loading
            setLoading(true);

            try {
                // Simulate delay
                await new Promise(resolve => setTimeout(resolve, 1000));

                // Get existing users
                const existingUsers = JSON.parse(localStorage.getItem('users') || '{"users":[]}');
                console.log('👥 Existing users:', existingUsers);

                // Check for duplicates
                const userExists = existingUsers.users.some(u => 
                    u.username.toLowerCase() === username.toLowerCase() || 
                    u.email.toLowerCase() === email.toLowerCase()
                );

                if (userExists) {
                    throw new Error('Username or email already exists');
                }

                // Create new user
                const newUser = {
                    id: Date.now(),
                    username: username.toLowerCase(),
                    email: email.toLowerCase(),
                    password: password,
                    createdAt: new Date().toISOString(),
                    lastLogin: null
                };

                // Add to users list
                existingUsers.users.push(newUser);
                localStorage.setItem('users', JSON.stringify(existingUsers));

                // Create user data file
                const userData = {
                    username: username.toLowerCase(),
                    memory: {},
                    savedChats: [],
                    settings: { theme: 'dark', notifications: true },
                    createdAt: new Date().toISOString(),
                    lastLogin: null
                };

                localStorage.setItem(`user_${username.toLowerCase()}`, JSON.stringify(userData));

                console.log('🎉 User created successfully!');
                showToast('Account created successfully! Redirecting...', 'success');

                // Redirect after 2 seconds
                setTimeout(() => {
                    window.location.href = `login.html?registered=true&username=${encodeURIComponent(username)}`;
                }, 2000);

            } catch (error) {
                console.error('💥 Registration error:', error);
                showToast(error.message, 'error');
            } finally {
                setLoading(false);
            }
        });

        function showError(field, message) {
            const errorEl = document.getElementById(field + 'Error');
            if (errorEl) {
                errorEl.textContent = message;
                errorEl.classList.remove('hidden');
                console.log(`🚨 Error for ${field}: ${message}`);
            }
        }

        function clearAllErrors() {
            ['username', 'email', 'password', 'confirmPassword', 'terms'].forEach(field => {
                const errorEl = document.getElementById(field + 'Error');
                if (errorEl) {
                    errorEl.classList.add('hidden');
                }
            });
        }

        function showToast(message, type = 'success') {
            const toast = document.getElementById('toast');
            const content = document.getElementById('toastContent');
            const messageEl = document.getElementById('toastMessage');

            messageEl.textContent = message;
            
            if (type === 'error') {
                content.className = 'bg-red-500 text-white px-6 py-3 rounded-lg shadow-lg';
            } else {
                content.className = 'bg-green-500 text-white px-6 py-3 rounded-lg shadow-lg';
            }

            toast.classList.remove('hidden');

            setTimeout(() => {
                toast.classList.add('hidden');
            }, 4000);
        }

        function setLoading(loading) {
            const btn = document.getElementById('registerBtn');
            const text = document.getElementById('registerText');
            const spinner = document.getElementById('registerSpinner');

            btn.disabled = loading;
            
            if (loading) {
                text.classList.add('hidden');
                spinner.classList.remove('hidden');
                console.log('⏳ Loading state: ON');
            } else {
                text.classList.remove('hidden');
                spinner.classList.add('hidden');
                console.log('✅ Loading state: OFF');
            }
        }
    </script>
</body>
</html>
