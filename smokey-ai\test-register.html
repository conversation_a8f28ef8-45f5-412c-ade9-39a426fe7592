<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Registration</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
        }
        button {
            background: #8b5cf6;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background: #7c3aed;
        }
        .error {
            color: red;
            font-size: 14px;
            margin-top: 5px;
        }
        .success {
            color: green;
            font-size: 14px;
            margin-top: 5px;
        }
        .debug {
            background: #f0f0f0;
            padding: 15px;
            margin-top: 20px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <h1>🧪 Registration Test</h1>
    <p>Simple test form to debug registration issues</p>

    <form id="testForm">
        <div class="form-group">
            <label for="username">Username:</label>
            <input type="text" id="username" placeholder="testuser" value="testuser">
        </div>

        <div class="form-group">
            <label for="email">Email:</label>
            <input type="email" id="email" placeholder="<EMAIL>" value="<EMAIL>">
        </div>

        <div class="form-group">
            <label for="password">Password:</label>
            <input type="password" id="password" placeholder="password123" value="password123">
        </div>

        <div class="form-group">
            <label for="confirmPassword">Confirm Password:</label>
            <input type="password" id="confirmPassword" placeholder="password123" value="password123">
        </div>

        <div class="form-group">
            <label>
                <input type="checkbox" id="terms" checked> I accept the terms
            </label>
        </div>

        <button type="submit">Create Account</button>
    </form>

    <div id="result"></div>

    <div class="debug">
        <h3>Debug Info:</h3>
        <div id="debugInfo">Ready to test...</div>
    </div>

    <script>
        // Simple registration test
        document.getElementById('testForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const debugDiv = document.getElementById('debugInfo');
            const resultDiv = document.getElementById('result');
            
            try {
                debugDiv.innerHTML = 'Starting test...<br>';
                
                // Get form data
                const username = document.getElementById('username').value.trim();
                const email = document.getElementById('email').value.trim();
                const password = document.getElementById('password').value;
                const confirmPassword = document.getElementById('confirmPassword').value;
                const terms = document.getElementById('terms').checked;
                
                debugDiv.innerHTML += `Form data: ${JSON.stringify({username, email, password: '***', confirmPassword: '***', terms})}<br>`;
                
                // Basic validation
                if (!username || username.length < 3) {
                    throw new Error('Username must be at least 3 characters');
                }
                
                if (!email || !email.includes('@')) {
                    throw new Error('Invalid email format');
                }
                
                if (!password || password.length < 6) {
                    throw new Error('Password must be at least 6 characters');
                }
                
                if (password !== confirmPassword) {
                    throw new Error('Passwords do not match');
                }
                
                if (!terms) {
                    throw new Error('Must accept terms');
                }
                
                debugDiv.innerHTML += 'Validation passed!<br>';
                
                // Load existing users
                const existingUsers = JSON.parse(localStorage.getItem('users') || '{"users":[]}');
                debugDiv.innerHTML += `Existing users: ${JSON.stringify(existingUsers)}<br>`;
                
                // Check for duplicates
                const userExists = existingUsers.users.some(u => 
                    u.username.toLowerCase() === username.toLowerCase() || 
                    u.email.toLowerCase() === email.toLowerCase()
                );
                
                if (userExists) {
                    throw new Error('Username or email already exists');
                }
                
                debugDiv.innerHTML += 'No duplicates found!<br>';
                
                // Create new user
                const newUser = {
                    id: Date.now(),
                    username: username.toLowerCase(),
                    email: email.toLowerCase(),
                    password: password,
                    createdAt: new Date().toISOString(),
                    lastLogin: null
                };
                
                // Add to users list
                existingUsers.users.push(newUser);
                localStorage.setItem('users', JSON.stringify(existingUsers));
                
                debugDiv.innerHTML += 'User added to main list!<br>';
                
                // Create user data file
                const userData = {
                    username: username.toLowerCase(),
                    memory: {},
                    savedChats: [],
                    settings: { theme: 'dark', notifications: true },
                    createdAt: new Date().toISOString(),
                    lastLogin: null
                };
                
                localStorage.setItem(`user_${username.toLowerCase()}`, JSON.stringify(userData));
                
                debugDiv.innerHTML += 'User data file created!<br>';
                debugDiv.innerHTML += `SUCCESS! User ${username} created.<br>`;
                
                resultDiv.innerHTML = '<div class="success">✅ Account created successfully!</div>';
                
                // Show what's in localStorage now
                debugDiv.innerHTML += '<br>Current localStorage:<br>';
                debugDiv.innerHTML += `users: ${localStorage.getItem('users')}<br>`;
                debugDiv.innerHTML += `user_${username.toLowerCase()}: ${localStorage.getItem(`user_${username.toLowerCase()}`)}<br>`;
                
            } catch (error) {
                debugDiv.innerHTML += `ERROR: ${error.message}<br>`;
                resultDiv.innerHTML = `<div class="error">❌ ${error.message}</div>`;
                console.error('Registration test error:', error);
            }
        });
        
        // Show current localStorage on load
        window.addEventListener('load', () => {
            const debugDiv = document.getElementById('debugInfo');
            debugDiv.innerHTML = 'Current localStorage:<br>';
            debugDiv.innerHTML += `users: ${localStorage.getItem('users') || 'null'}<br>`;
            
            // Show all user files
            for (let i = 0; i < localStorage.length; i++) {
                const key = localStorage.key(i);
                if (key.startsWith('user_')) {
                    debugDiv.innerHTML += `${key}: ${localStorage.getItem(key)}<br>`;
                }
            }
        });
    </script>
</body>
</html>
