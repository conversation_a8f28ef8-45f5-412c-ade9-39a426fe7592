<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Capabilities Showcase</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-logo">
                <i class="fas fa-robot"></i>
                <span>AI Showcase</span>
            </div>
            <ul class="nav-menu">
                <li><a href="#home" class="nav-link">Home</a></li>
                <li><a href="#capabilities" class="nav-link">Capabilities</a></li>
                <li><a href="#demos" class="nav-link">Demos</a></li>
                <li><a href="#tools" class="nav-link">Tools</a></li>
                <li><a href="#contact" class="nav-link">Contact</a></li>
            </ul>
            <div class="hamburger">
                <span></span>
                <span></span>
                <span></span>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section id="home" class="hero">
        <div class="hero-container">
            <div class="hero-content">
                <h1 class="hero-title">👋 Meet Auggie</h1>
                <p class="hero-subtitle">Your AI coding assistant powered by Augment Code's world-leading context engine</p>
                <div class="hero-description">
                    <p>I excel at understanding large, complex codebases and can help with everything from code development to project management. Built on Claude Sonnet 4 by Anthropic.</p>
                </div>
                <div class="hero-stats">
                    <div class="stat">
                        <span class="stat-number">17+</span>
                        <span class="stat-label">Powerful Tools</span>
                    </div>
                    <div class="stat">
                        <span class="stat-number">∞</span>
                        <span class="stat-label">Code Understanding</span>
                    </div>
                    <div class="stat">
                        <span class="stat-number">24/7</span>
                        <span class="stat-label">Ready to Help</span>
                    </div>
                </div>
                <button class="cta-button" onclick="scrollToSection('capabilities')">
                    Explore My Capabilities
                    <i class="fas fa-arrow-right"></i>
                </button>
            </div>
            <div class="hero-visual">
                <div class="floating-elements">
                    <div class="floating-element" style="--delay: 0s">🤖</div>
                    <div class="floating-element" style="--delay: 1s">📝</div>
                    <div class="floating-element" style="--delay: 2s">🔍</div>
                    <div class="floating-element" style="--delay: 3s">⚡</div>
                    <div class="floating-element" style="--delay: 4s">🧠</div>
                    <div class="floating-element" style="--delay: 5s">🌐</div>
                </div>
            </div>
        </div>
    </section>

    <!-- Capabilities Section -->
    <section id="capabilities" class="capabilities">
        <div class="container">
            <h2 class="section-title">Core Capabilities</h2>
            <div class="capabilities-grid">
                <div class="capability-card">
                    <div class="capability-icon">
                        <i class="fas fa-code"></i>
                    </div>
                    <h3>Code Development</h3>
                    <p>Write, debug, and optimize code in multiple programming languages including JavaScript, Python, HTML, CSS, and more.</p>
                    <ul class="capability-features">
                        <li>Full-stack web development</li>
                        <li>Code review and optimization</li>
                        <li>Bug fixing and debugging</li>
                        <li>Best practices implementation</li>
                    </ul>
                </div>

                <div class="capability-card">
                    <div class="capability-icon">
                        <i class="fas fa-file-alt"></i>
                    </div>
                    <h3>File Management</h3>
                    <p>Create, read, edit, and organize files and directories with precision and efficiency.</p>
                    <ul class="capability-features">
                        <li>File creation and editing</li>
                        <li>Directory structure management</li>
                        <li>Content search and replace</li>
                        <li>Project organization</li>
                    </ul>
                </div>

                <div class="capability-card">
                    <div class="capability-icon">
                        <i class="fas fa-terminal"></i>
                    </div>
                    <h3>Command Line</h3>
                    <p>Execute system commands, manage processes, and automate tasks through command line interfaces.</p>
                    <ul class="capability-features">
                        <li>System command execution</li>
                        <li>Process management</li>
                        <li>Automation scripts</li>
                        <li>Environment setup</li>
                    </ul>
                </div>

                <div class="capability-card">
                    <div class="capability-icon">
                        <i class="fas fa-globe"></i>
                    </div>
                    <h3>Web Interaction</h3>
                    <p>Navigate websites, interact with web elements, and perform automated browser actions.</p>
                    <ul class="capability-features">
                        <li>Browser automation</li>
                        <li>Web scraping</li>
                        <li>UI testing</li>
                        <li>Form interactions</li>
                    </ul>
                </div>

                <div class="capability-card">
                    <div class="capability-icon">
                        <i class="fas fa-search"></i>
                    </div>
                    <h3>Data Analysis</h3>
                    <p>Search through files, analyze patterns, and extract meaningful insights from data.</p>
                    <ul class="capability-features">
                        <li>Regex pattern matching</li>
                        <li>Code analysis</li>
                        <li>Data extraction</li>
                        <li>Pattern recognition</li>
                    </ul>
                </div>

                <div class="capability-card">
                    <div class="capability-icon">
                        <i class="fas fa-puzzle-piece"></i>
                    </div>
                    <h3>Problem Solving</h3>
                    <p>Break down complex problems into manageable steps and provide systematic solutions.</p>
                    <ul class="capability-features">
                        <li>Task decomposition</li>
                        <li>Strategic planning</li>
                        <li>Error diagnosis</li>
                        <li>Solution optimization</li>
                    </ul>
                </div>
            </div>
        </div>
    </section>

    <!-- Interactive Demos Section -->
    <section id="demos" class="demos">
        <div class="container">
            <h2 class="section-title">Interactive Demonstrations</h2>
            <div class="demos-grid">
                <div class="demo-card">
                    <h3>Color Palette Generator</h3>
                    <div class="color-demo">
                        <div class="color-palette" id="colorPalette"></div>
                        <button class="demo-button" onclick="generateColors()">Generate New Palette</button>
                    </div>
                </div>

                <div class="demo-card">
                    <h3>Text Analyzer</h3>
                    <div class="text-demo">
                        <textarea id="textInput" placeholder="Enter text to analyze..."></textarea>
                        <div class="text-stats" id="textStats">
                            <span>Words: <strong id="wordCount">0</strong></span>
                            <span>Characters: <strong id="charCount">0</strong></span>
                            <span>Lines: <strong id="lineCount">0</strong></span>
                        </div>
                    </div>
                </div>

                <div class="demo-card">
                    <h3>Interactive Calculator</h3>
                    <div class="calculator-demo">
                        <div class="calculator">
                            <div class="calc-display" id="calcDisplay">0</div>
                            <div class="calc-buttons">
                                <button onclick="clearCalc()">C</button>
                                <button onclick="appendToCalc('/')">/</button>
                                <button onclick="appendToCalc('*')">×</button>
                                <button onclick="deleteLast()">⌫</button>
                                <button onclick="appendToCalc('7')">7</button>
                                <button onclick="appendToCalc('8')">8</button>
                                <button onclick="appendToCalc('9')">9</button>
                                <button onclick="appendToCalc('-')">-</button>
                                <button onclick="appendToCalc('4')">4</button>
                                <button onclick="appendToCalc('5')">5</button>
                                <button onclick="appendToCalc('6')">6</button>
                                <button onclick="appendToCalc('+')">+</button>
                                <button onclick="appendToCalc('1')">1</button>
                                <button onclick="appendToCalc('2')">2</button>
                                <button onclick="appendToCalc('3')">3</button>
                                <button onclick="calculate()" class="calc-equals" rowspan="2">=</button>
                                <button onclick="appendToCalc('0')" class="calc-zero">0</button>
                                <button onclick="appendToCalc('.')">.</button>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="demo-card">
                    <h3>Dynamic Chart</h3>
                    <div class="chart-demo">
                        <canvas id="dynamicChart" width="300" height="200"></canvas>
                        <button class="demo-button" onclick="updateChart()">Update Data</button>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Tools Section -->
    <section id="tools" class="tools">
        <div class="container">
            <h2 class="section-title">Available Tools</h2>
            <div class="tools-grid">
                <div class="tool-item">
                    <i class="fas fa-file-code"></i>
                    <span>write_to_file</span>
                </div>
                <div class="tool-item">
                    <i class="fas fa-edit"></i>
                    <span>replace_in_file</span>
                </div>
                <div class="tool-item">
                    <i class="fas fa-folder-open"></i>
                    <span>read_file</span>
                </div>
                <div class="tool-item">
                    <i class="fas fa-list"></i>
                    <span>list_files</span>
                </div>
                <div class="tool-item">
                    <i class="fas fa-terminal"></i>
                    <span>execute_command</span>
                </div>
                <div class="tool-item">
                    <i class="fas fa-search"></i>
                    <span>search_files</span>
                </div>
                <div class="tool-item">
                    <i class="fas fa-globe"></i>
                    <span>browser_action</span>
                </div>
                <div class="tool-item">
                    <i class="fas fa-question-circle"></i>
                    <span>ask_followup_question</span>
                </div>
                <div class="tool-item">
                    <i class="fas fa-code-branch"></i>
                    <span>list_code_definition_names</span>
                </div>
                <div class="tool-item">
                    <i class="fas fa-check-circle"></i>
                    <span>attempt_completion</span>
                </div>
                <div class="tool-item">
                    <i class="fas fa-plug"></i>
                    <span>use_mcp_tool</span>
                </div>
                <div class="tool-item">
                    <i class="fas fa-database"></i>
                    <span>access_mcp_resource</span>
                </div>
            </div>
        </div>
    </section>

    <!-- Contact Section -->
    <section id="contact" class="contact">
        <div class="container">
            <h2 class="section-title">Get Started</h2>
            <div class="contact-content">
                <div class="contact-info">
                    <h3>Ready to explore AI capabilities?</h3>
                    <p>This showcase demonstrates just a fraction of what's possible with AI assistance. From simple file operations to complex web development, the possibilities are endless.</p>
                    <div class="contact-features">
                        <div class="feature">
                            <i class="fas fa-lightning-bolt"></i>
                            <span>Instant responses</span>
                        </div>
                        <div class="feature">
                            <i class="fas fa-cogs"></i>
                            <span>Multi-tool integration</span>
                        </div>
                        <div class="feature">
                            <i class="fas fa-brain"></i>
                            <span>Intelligent problem solving</span>
                        </div>
                    </div>
                </div>
                <div class="contact-form">
                    <h4>Try It Out</h4>
                    <form id="demoForm">
                        <input type="text" placeholder="Your name" required>
                        <input type="email" placeholder="Your email" required>
                        <textarea placeholder="What would you like to build or explore?" required></textarea>
                        <button type="submit">Start Exploring</button>
                    </form>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h4>AI Capabilities</h4>
                    <p>Showcasing the power of artificial intelligence in web development and beyond.</p>
                </div>
                <div class="footer-section">
                    <h4>Features</h4>
                    <ul>
                        <li>Code Generation</li>
                        <li>File Management</li>
                        <li>Web Automation</li>
                        <li>Problem Solving</li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>Technologies</h4>
                    <ul>
                        <li>HTML5 & CSS3</li>
                        <li>JavaScript ES6+</li>
                        <li>Responsive Design</li>
                        <li>Modern Web APIs</li>
                    </ul>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 AI Capabilities Showcase. Built with intelligence and creativity.</p>
            </div>
        </div>
    </footer>

    <script src="script.js"></script>
</body>
</html>
