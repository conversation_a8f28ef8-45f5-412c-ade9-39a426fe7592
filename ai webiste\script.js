document.addEventListener('DOMContentLoaded', function() {
    // Loading animation
    setTimeout(() => {
        document.querySelector('.loader').classList.add('hidden');
    }, 1500);

    // Theme toggle functionality
    const themeToggle = document.getElementById('toggle');
    const currentTheme = localStorage.getItem('theme');
    
    if (currentTheme) {
        document.documentElement.setAttribute('data-theme', currentTheme);
        if (currentTheme === 'dark') {
            themeToggle.checked = true;
        }
    }
    
    themeToggle.addEventListener('change', function() {
        if (this.checked) {
            document.documentElement.setAttribute('data-theme', 'dark');
            localStorage.setItem('theme', 'dark');
        } else {
            document.documentElement.setAttribute('data-theme', 'light');
            localStorage.setItem('theme', 'light');
        }
    });

    // Smooth scrolling for navigation links
    document.querySelectorAll('nav a').forEach(anchor => {
        anchor.addEventListener('click', function(e) {
            e.preventDefault();
            const targetId = this.getAttribute('href');
            const targetElement = document.querySelector(targetId);
            
            window.scrollTo({
                top: targetElement.offsetTop - 80,
                behavior: 'smooth'
            });
        });
    });

    // Back to top button
    const backToTopButton = document.querySelector('.back-to-top');
    window.addEventListener('scroll', function() {
        if (window.pageYOffset > 300) {
            backToTopButton.classList.add('visible');
        } else {
            backToTopButton.classList.remove('visible');
        }
    });
    
    backToTopButton.addEventListener('click', function(e) {
        e.preventDefault();
        window.scrollTo({
            top: 0,
            behavior: 'smooth'
        });
    });

    // Scroll reveal animations
    function revealOnScroll() {
        const reveals = document.querySelectorAll('.reveal');
        
        reveals.forEach(reveal => {
            const revealTop = reveal.getBoundingClientRect().top;
            const windowHeight = window.innerHeight;
            
            if (revealTop < windowHeight - 100) {
                reveal.classList.add('active');
            }
        });
    }
    
    window.addEventListener('scroll', revealOnScroll);
    revealOnScroll(); // Initialize on load

    // About section fade-in
    const aboutContent = document.querySelector('.about-content');
    window.addEventListener('scroll', function() {
        const aboutTop = aboutContent.getBoundingClientRect().top;
        const windowHeight = window.innerHeight;
        
        if (aboutTop < windowHeight - 100) {
            aboutContent.classList.add('visible');
        }
    });

    // Animation controls
    document.querySelectorAll('.animation-control').forEach(button => {
        button.addEventListener('click', function() {
            const animationType = this.getAttribute('data-animation');
            const animationBox = this.closest('.animation-card').querySelector('.animation-box');
            
            switch(animationType) {
                case 'rotate':
                    toggleRotateAnimation(animationBox);
                    break;
                case 'bounce':
                    toggleBounceAnimation(animationBox);
                    break;
                case 'fade':
                    toggleFadeAnimation(animationBox);
                    break;
                case 'morph':
                    triggerMorphAnimation(animationBox);
                    break;
                case 'threejs':
                    toggleThreeJSRotation();
                    break;
                case 'particle':
                    resetParticleAnimation();
                    break;
            }
        });
    });

    // Animation control functions
    function toggleRotateAnimation(container) {
        const element = container.querySelector('.rotating-element');
        const isPaused = element.style.animationPlayState === 'paused';
        
        element.style.animationPlayState = isPaused ? 'running' : 'paused';
        container.closest('.animation-card').querySelector('.animation-control').textContent = 
            isPaused ? 'Pause Rotation' : 'Resume Rotation';
    }

    function toggleBounceAnimation(container) {
        const element = container.querySelector('.bouncing-ball');
        element.style.animation = element.style.animation ? '' : 'bounce 0.5s ease-in-out infinite alternate';
        
        container.closest('.animation-card').querySelector('.animation-control').textContent = 
            element.style.animation ? 'Stop Bounce' : 'Start Bounce';
    }

    function toggleFadeAnimation(container) {
        const element = container.querySelector('.fading-element');
        element.style.animation = element.style.animation ? '' : 'fadePulse 1.5s ease-in-out infinite';
        
        container.closest('.animation-card').querySelector('.animation-control').textContent = 
            element.style.animation ? 'Stop Fade' : 'Start Fade';
    }

    function triggerMorphAnimation(container) {
        const element = container.querySelector('.morphing-element');
        element.style.borderRadius = element.style.borderRadius === '0%' ? '50%' : '0%';
        element.style.width = element.style.width === '80px' ? '100px' : '80px';
        element.style.height = element.style.height === '80px' ? '100px' : '80px';
        element.style.backgroundColor = getRandomColor();
        
        // Animate with GSAP
        gsap.to(element, {
            borderRadius: element.style.borderRadius,
            width: element.style.width,
            height: element.style.height,
            backgroundColor: element.style.backgroundColor,
            duration: 0.8,
            ease: "elastic.out(1, 0.5)"
        });
    }

    function getRandomColor() {
        const colors = ['#4361ee', '#3f37c9', '#4cc9f0', '#4895ef', '#560bad'];
        return colors[Math.floor(Math.random() * colors.length)];
    }

    // Three.js 3D Animation
    let threeJSScene, threeJSCamera, threeJSRenderer, threeJSSphere;
    let isThreeJSRotating = true;
    
    function initThreeJS() {
        const container = document.getElementById('threejs-container');
        
        // Scene
        threeJSScene = new THREE.Scene();
        
        // Camera
        threeJSCamera = new THREE.PerspectiveCamera(75, container.clientWidth / container.clientHeight, 0.1, 1000);
        threeJSCamera.position.z = 5;
        
        // Renderer
        threeJSRenderer = new THREE.WebGLRenderer({ antialias: true, alpha: true });
        threeJSRenderer.setSize(container.clientWidth, container.clientHeight);
        container.appendChild(threeJSRenderer.domElement);
        
        // Geometry
        const geometry = new THREE.SphereGeometry(1.5, 32, 32);
        const material = new THREE.MeshPhongMaterial({ 
            color: 0x4361ee,
            shininess: 100,
            specular: 0x111111
        });
        threeJSSphere = new THREE.Mesh(geometry, material);
        threeJSScene.add(threeJSSphere);
        
        // Lights
        const ambientLight = new THREE.AmbientLight(0x404040);
        threeJSScene.add(ambientLight);
        
        const directionalLight = new THREE.DirectionalLight(0xffffff, 1);
        directionalLight.position.set(1, 1, 1);
        threeJSScene.add(directionalLight);
        
        // Animation loop
        function animate() {
            requestAnimationFrame(animate);
            
            if (isThreeJSRotating) {
                threeJSSphere.rotation.x += 0.01;
                threeJSSphere.rotation.y += 0.01;
            }
            
            threeJSRenderer.render(threeJSScene, threeJSCamera);
        }
        
        animate();
        
        // Handle window resize
        window.addEventListener('resize', function() {
            threeJSCamera.aspect = container.clientWidth / container.clientHeight;
            threeJSCamera.updateProjectionMatrix();
            threeJSRenderer.setSize(container.clientWidth, container.clientHeight);
        });
    }
    
    function toggleThreeJSRotation() {
        isThreeJSRotating = !isThreeJSRotating;
        document.querySelector('[data-animation="threejs"]').textContent = 
            isThreeJSRotating ? 'Pause Rotation' : 'Resume Rotation';
    }

    // Particle Animation
    let particleCanvas, particleCtx, particles = [];
    
    function initParticleAnimation() {
        particleCanvas = document.getElementById('particle-canvas');
        particleCtx = particleCanvas.getContext('2d');
        
        // Set canvas size
        particleCanvas.width = particleCanvas.offsetWidth;
        particleCanvas.height = particleCanvas.offsetHeight;
        
        // Create particles
        for (let i = 0; i < 50; i++) {
            particles.push({
                x: Math.random() * particleCanvas.width,
                y: Math.random() * particleCanvas.height,
                size: Math.random() * 3 + 1,
                speedX: Math.random() * 2 - 1,
                speedY: Math.random() * 2 - 1,
                color: `rgba(67, 97, 238, ${Math.random() * 0.5 + 0.1})`
            });
        }
        
        // Animation loop
        function animateParticles() {
            particleCtx.clearRect(0, 0, particleCanvas.width, particleCanvas.height);
            
            for (let i = 0; i < particles.length; i++) {
                const p = particles[i];
                
                particleCtx.beginPath();
                particleCtx.arc(p.x, p.y, p.size, 0, Math.PI * 2);
                particleCtx.fillStyle = p.color;
                particleCtx.fill();
                
                // Update position
                p.x += p.speedX;
                p.y += p.speedY;
                
                // Bounce off edges
                if (p.x < 0 || p.x > particleCanvas.width) p.speedX *= -1;
                if (p.y < 0 || p.y > particleCanvas.height) p.speedY *= -1;
            }
            
            requestAnimationFrame(animateParticles);
        }
        
        animateParticles();
        
        // Handle window resize
        window.addEventListener('resize', function() {
            particleCanvas.width = particleCanvas.offsetWidth;
            particleCanvas.height = particleCanvas.offsetHeight;
        });
    }
    
    function resetParticleAnimation() {
        particles = [];
        initParticleAnimation();
    }

    // Contact form submission
    const contactForm = document.getElementById('contactForm');
    if (contactForm) {
        contactForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const name = document.getElementById('name').value;
            const email = document.getElementById('email').value;
            const message = document.getElementById('message').value;
            
            // Here you would typically send the data to a server
            console.log('Form submitted:', { name, email, message });
            
            // Show success animation
            const submitBtn = this.querySelector('.submit-btn');
            submitBtn.innerHTML = '<i class="fas fa-check"></i> Sent!';
            
            setTimeout(() => {
                submitBtn.innerHTML = '<span>Send Message</span><i class="fas fa-paper-plane"></i>';
                this.reset();
            }, 2000);
        });
    }

    // Initialize animations after page load
    setTimeout(() => {
        initThreeJS();
        initParticleAnimation();
    }, 100);
});