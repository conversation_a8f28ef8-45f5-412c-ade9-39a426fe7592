const startChattingButton = document.getElementById('start-chatting');
const chatSection = document.querySelector('.chat-section');
const userInput = document.getElementById('user-input');
const sendButton = document.getElementById('send-button');
const messagesContainer = document.querySelector('.messages');

startChattingButton.addEventListener('click', function() {
    chatSection.classList.add('active');
});

sendButton.addEventListener('click', function() {
    const messageText = userInput.value;
    if (messageText.trim() !== '') {
        addUserMessage(messageText);
        userInput.value = '';
        sendToGemini(messageText);
    }
});

userInput.addEventListener('keydown', function(event) {
    if (event.key === 'Enter') {
        sendButton.click();
    }
});

function addUserMessage(message) {
    const messageElement = document.createElement('div');
    messageElement.classList.add('message', 'user');
    messageElement.textContent = message;
    messagesContainer.appendChild(messageElement);
    scrollToBottom();
}

function addBotMessage(message) {
    const messageElement = document.createElement('div');
    messageElement.classList.add('message', 'bot');
    messageElement.textContent = message;
    messagesContainer.appendChild(messageElement);
    scrollToBottom();
}

function scrollToBottom() {
    messagesContainer.scrollTop = messagesContainer.scrollHeight;
}

function sendToGemini(message) {
    sendButton.disabled = true;
    addBotMessage('Thinking...');

    fetch('https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent?key=' + window.config.GEMINI_API_KEY, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            contents: [{
                parts: [{ text: message }]
            }]
        })
        .then(function(response) {
            return response.json();
        })
        .then(function(data) {
            const botResponse = data.candidates[0].content.parts[0].text;
            addBotMessage(botResponse);
        })
        .catch(function(error) {
            console.error('Error:', error);
            addBotMessage('Error: Could not get response from Smokey AI.');
        })
        .finally(function() {
            sendButton.disabled = false;
        });
}

const themeToggle = document.getElementById('theme-toggle');
// Add event listener to the theme toggle button
themeToggle.addEventListener('click', function() {
    // Toggle the light-mode class on the body
    document.body.classList.toggle('light-mode');
});
