:root {
    /* Dark Theme Colors */
    --bg-primary: #0f172a;
    --bg-secondary: #1e293b;
    --bg-tertiary: #334155;
    --text-primary: #f8fafc;
    --text-secondary: #e2e8f0;
    --text-tertiary: #94a3b8;
    --accent-primary: #38bdf8;
    --accent-secondary: #818cf8;
    --error: #f87171;
    --success: #4ade80;
    --warning: #fbbf24;
    
    /* Weather Condition Colors */
    --sunny: #f59e0b;
    --cloudy: #94a3b8;
    --rainy: #60a5fa;
    --snowy: #bfdbfe;
    --stormy: #7c3aed;
    --foggy: #cbd5e1;
    
    /* Shadows */
    --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.1);
    --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
    --shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.1);
    
    /* Transitions */
    --transition-fast: all 0.15s ease;
    --transition-normal: all 0.3s ease;
    --transition-slow: all 0.5s ease;
    
    /* Border Radius */
    --radius-sm: 0.5rem;
    --radius-md: 1rem;
    --radius-lg: 1.5rem;
    --radius-full: 9999px;
}

[data-theme="light"] {
    --bg-primary: #f8fafc;
    --bg-secondary: #e2e8f0;
    --bg-tertiary: #cbd5e1;
    --text-primary: #0f172a;
    --text-secondary: #1e293b;
    --text-tertiary: #334155;
    --accent-primary: #0369a1;
    --accent-secondary: #4f46e5;
    
    /* Light theme weather colors are more vibrant */
    --sunny: #f97316;
    --cloudy: #64748b;
    --rainy: #3b82f6;
    --snowy: #93c5fd;
    --stormy: #6d28d9;
    --foggy: #9ca3af;
}

/* Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Poppins', sans-serif;
    background-color: var(--bg-primary);
    color: var(--text-primary);
    line-height: 1.6;
    transition: var(--transition-normal);
    min-height: 100vh;
    overflow-x: hidden;
}

.app-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 2rem;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    position: relative;
}

/* Header Styles */
.app-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-end;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

[data-theme="light"] .app-header {
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.header-content {
    flex: 1;
}

.app-title {
    font-family: 'Fredoka One', cursive;
    font-size: 2.5rem;
    background: linear-gradient(135deg, var(--accent-primary), var(--accent-secondary));
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.app-title i {
    font-size: 2.2rem;
}

.app-subtitle {
    color: var(--text-tertiary);
    font-weight: 300;
    font-size: 1rem;
}

.header-controls {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.control-btn {
    background: none;
    border: none;
    color: var(--text-primary);
    font-size: 1.2rem;
    cursor: pointer;
    width: 2.5rem;
    height: 2.5rem;
    border-radius: var(--radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition-fast);
}

.control-btn:hover {
    background-color: var(--bg-tertiary);
}

.unit-toggle {
    display: flex;
    background-color: var(--bg-secondary);
    border-radius: var(--radius-full);
    padding: 0.25rem;
}

.unit-btn {
    border: none;
    background: none;
    color: var(--text-secondary);
    font-weight: 600;
    padding: 0.35rem 0.75rem;
    border-radius: var(--radius-full);
    cursor: pointer;
    transition: var(--transition-fast);
}

.unit-btn.active {
    background-color: var(--accent-primary);
    color: white;
}

/* Search Section */
.search-section {
    margin-bottom: 2rem;
}

.search-container {
    max-width: 800px;
    margin: 0 auto;
}

.search-input-container {
    display: flex;
    position: relative;
    margin-bottom: 0.5rem;
}

.search-icon {
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-tertiary);
}

.city-input {
    flex: 1;
    padding: 1rem 1rem 1rem 3rem;
    border: none;
    border-radius: var(--radius-md);
    background-color: var(--bg-secondary);
    color: var(--text-primary);
    font-size: 1rem;
    transition: var(--transition-normal);
    box-shadow: var(--shadow-sm);
}

.city-input:focus {
    outline: none;
    box-shadow: 0 0 0 2px var(--accent-primary);
    background-color: var(--bg-tertiary);
}

.add-city-btn {
    margin-left: 0.5rem;
    padding: 0 1.5rem;
    background-color: var(--accent-primary);
    color: white;
    border: none;
    border-radius: var(--radius-md);
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition-fast);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.add-city-btn:hover {
    background-color: var(--accent-secondary);
    transform: translateY(-1px);
}

.add-city-btn:active {
    transform: translateY(0);
}

.search-hint {
    font-size: 0.85rem;
    color: var(--text-tertiary);
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

/* Weather Cards Section */
.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.section-header h2 {
    font-size: 1.25rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.refresh-btn {
    background: none;
    border: none;
    color: var(--accent-primary);
    font-weight: 500;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: var(--transition-fast);
}

.refresh-btn:hover {
    color: var(--accent-secondary);
}

.refresh-btn i {
    transition: transform 0.5s ease;
}

.refresh-btn:hover i {
    transform: rotate(180deg);
}

.weather-cards {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1.5rem;
    margin-bottom: 3rem;
}

/* Weather Card Styles */
.weather-card {
    background-color: var(--bg-secondary);
    border-radius: var(--radius-md);
    padding: 1.5rem;
    box-shadow: var(--shadow-md);
    transition: var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.weather-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.weather-card-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1rem;
}

.weather-card-city {
    font-size: 1.25rem;
    font-weight: 600;
}

.weather-card-country {
    font-size: 0.85rem;
    color: var(--text-tertiary);
    margin-top: 0.25rem;
}

.weather-card-delete {
    background: none;
    border: none;
    color: var(--text-tertiary);
    cursor: pointer;
    transition: var(--transition-fast);
    padding: 0.25rem;
    border-radius: var(--radius-full);
}

.weather-card-delete:hover {
    color: var(--error);
    background-color: rgba(248, 113, 113, 0.1);
}

.weather-card-main {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.weather-card-temp {
    font-size: 3rem;
    font-weight: 700;
}

.weather-card-icon {
    font-size: 4rem;
    margin-right: 1rem;
}

.weather-card-details {
    display: flex;
    justify-content: space-between;
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

[data-theme="light"] .weather-card-details {
    border-top: 1px solid rgba(0, 0, 0, 0.1);
}

.weather-card-detail {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.weather-card-detail i {
    font-size: 1.25rem;
    margin-bottom: 0.25rem;
}

.weather-card-detail-label {
    font-size: 0.75rem;
    color: var(--text-tertiary);
}

.weather-card-detail-value {
    font-weight: 600;
    font-size: 0.9rem;
}

/* Weather Condition Specific Styling */
.weather-card.sunny {
    background: linear-gradient(135deg, rgba(245, 158, 11, 0.1), rgba(251, 191, 36, 0.05));
}

.weather-card.cloudy {
    background: linear-gradient(135deg, rgba(148, 163, 184, 0.1), rgba(100, 116, 139, 0.05));
}

.weather-card.rainy {
    background: linear-gradient(135deg, rgba(96, 165, 250, 0.1), rgba(59, 130, 246, 0.05));
}

.weather-card.snowy {
    background: linear-gradient(135deg, rgba(191, 219, 254, 0.1), rgba(147, 197, 253, 0.05));
}

.weather-card.stormy {
    background: linear-gradient(135deg, rgba(124, 58, 237, 0.1), rgba(109, 40, 217, 0.05));
}

.weather-card.foggy {
    background: linear-gradient(135deg, rgba(203, 213, 225, 0.1), rgba(156, 163, 175, 0.05));
}

/* Welcome Card */
.welcome-card {
    background-color: var(--bg-secondary);
    border-radius: var(--radius-md);
    padding: 2rem;
    box-shadow: var(--shadow-md);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    grid-column: 1 / -1;
}

.welcome-icon {
    font-size: 4rem;
    color: var(--accent-primary);
    margin-bottom: 1rem;
}

.welcome-card h3 {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
}

.welcome-card p {
    color: var(--text-tertiary);
    margin-bottom: 1.5rem;
    max-width: 500px;
}

.sample-cities {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
    justify-content: center;
}

.sample-city {
    background-color: var(--bg-tertiary);
    border: none;
    color: var(--text-primary);
    padding: 0.5rem 1rem;
    border-radius: var(--radius-full);
    cursor: pointer;
    transition: var(--transition-fast);
    font-size: 0.85rem;
}

.sample-city:hover {
    background-color: var(--accent-primary);
    color: white;
}

/* Forecast Section */
.forecast-section {
    margin-top: 2rem;
    display: none;
}

.forecast-city {
    font-size: 0.9rem;
    color: var(--text-tertiary);
}

.forecast-cards {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 1rem;
}

.forecast-card {
    background-color: var(--bg-secondary);
    border-radius: var(--radius-md);
    padding: 1.5rem;
    text-align: center;
    box-shadow: var(--shadow-sm);
}

.forecast-day {
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.forecast-date {
    font-size: 0.8rem;
    color: var(--text-tertiary);
    margin-bottom: 1rem;
}

.forecast-icon {
    font-size: 2.5rem;
    margin-bottom: 1rem;
}

.forecast-temp {
    display: flex;
    justify-content: center;
    gap: 1rem;
}

.forecast-temp-high {
    font-weight: 600;
}

.forecast-temp-low {
    color: var(--text-tertiary);
}

/* Footer */
.app-footer {
    margin-top: auto;
    padding-top: 2rem;
    text-align: center;
    color: var(--text-tertiary);
    font-size: 0.85rem;
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.last-updated {
    font-size: 0.75rem;
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(15, 23, 42, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    opacity: 0;
    pointer-events: none;
    transition: var(--transition-normal);
}

.loading-overlay.active {
    opacity: 1;
    pointer-events: all;
}

.loading-content {
    text-align: center;
    color: white;
}

.weather-spinner {
    position: relative;
    width: 100px;
    height: 100px;
    margin: 0 auto 1rem;
}

.weather-spinner i {
    position: absolute;
    font-size: 2.5rem;
    opacity: 0;
}

.weather-spinner i:nth-child(1) {
    animation: weatherSpin 3s infinite;
    color: var(--sunny);
}

.weather-spinner i:nth-child(2) {
    animation: weatherSpin 3s infinite 1s;
    color: var(--cloudy);
}

.weather-spinner i:nth-child(3) {
    animation: weatherSpin 3s infinite 2s;
    color: var(--rainy);
}

@keyframes weatherSpin {
    0%, 100% {
        transform: translateY(0) rotate(0deg);
        opacity: 0;
    }
    10%, 30% {
        transform: translateY(-20px) rotate(10deg);
        opacity: 1;
    }
    20% {
        transform: translateY(-30px) rotate(0deg);
        opacity: 1;
    }
    40% {
        transform: translateY(0) rotate(-10deg);
        opacity: 0;
    }
}

/* Weather Animations */
.weather-animation {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
    z-index: -1;
    overflow: hidden;
}

.weather-animation .sun {
    position: absolute;
    width: 100px;
    height: 100px;
    background: radial-gradient(circle, rgba(255,215,0,0.8) 0%, rgba(255,165,0,0.4) 70%, transparent 100%);
    border-radius: 50%;
    top: 20%;
    left: 10%;
    animation: float 20s infinite alternate ease-in-out;
}

.weather-animation .cloud {
    position: absolute;
    background: rgba(255, 255, 255, 0.7);
    border-radius: 50%;
    filter: blur(5px);
}

.weather-animation .rain {
    position: absolute;
    width: 2px;
    background: linear-gradient(to bottom, transparent, rgba(173, 216, 230, 0.7));
    animation: rainFall linear infinite;
}

.weather-animation .snow {
    position: absolute;
    width: 6px;
    height: 6px;
    background: white;
    border-radius: 50%;
    filter: blur(1px);
    animation: snowFall linear infinite;
}

@keyframes float {
    0%, 100% {
        transform: translateY(0) translateX(0);
    }
    50% {
        transform: translateY(-20px) translateX(20px);
    }
}

@keyframes rainFall {
    0% {
        transform: translateY(-100vh);
    }
    100% {
        transform: translateY(100vh);
    }
}

@keyframes snowFall {
    0% {
        transform: translateY(-100vh) translateX(0);
        opacity: 1;
    }
    100% {
        transform: translateY(100vh) translateX(50px);
        opacity: 0;
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .app-container {
        padding: 1.5rem;
    }
    
    .app-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }
    
    .header-controls {
        width: 100%;
        justify-content: space-between;
    }
    
    .weather-cards {
        grid-template-columns: 1fr;
    }
    
    .search-input-container {
        flex-direction: column;
    }
    
    .add-city-btn {
        margin-left: 0;
        margin-top: 0.5rem;
        width: 100%;
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .app-container {
        padding: 1rem;
    }
    
    .app-title {
        font-size: 2rem;
    }
    
    .weather-card {
        padding: 1rem;
    }
    
    .weather-card-temp {
        font-size: 2.5rem;
    }
    
    .weather-card-icon {
        font-size: 3rem;
    }
}