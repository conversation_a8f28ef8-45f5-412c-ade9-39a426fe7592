class RegistrationManager {
    constructor() {
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.setupPasswordToggle();
        this.setupPasswordStrength();
    }

    setupEventListeners() {
        const form = document.getElementById('registerForm');
        const usernameInput = document.getElementById('username');
        const emailInput = document.getElementById('email');
        const passwordInput = document.getElementById('password');
        const confirmPasswordInput = document.getElementById('confirmPassword');

        // Form submission
        form.addEventListener('submit', (e) => this.handleSubmit(e));

        // Real-time validation
        usernameInput.addEventListener('blur', () => this.validateUsername());
        usernameInput.addEventListener('input', () => {
            this.clearError('username');
            this.checkUsernameAvailability();
        });

        emailInput.addEventListener('blur', () => this.validateEmail());
        emailInput.addEventListener('input', () => this.clearError('email'));

        passwordInput.addEventListener('input', () => {
            this.clearError('password');
            this.updatePasswordStrength();
            this.validatePasswordMatch();
        });

        confirmPasswordInput.addEventListener('input', () => {
            this.clearError('confirmPassword');
            this.validatePasswordMatch();
        });
    }

    setupPasswordToggle() {
        const toggleBtn = document.getElementById('togglePassword');
        const passwordInput = document.getElementById('password');
        const eyeIcon = document.getElementById('eyeIcon');

        toggleBtn.addEventListener('click', () => {
            const isPassword = passwordInput.type === 'password';
            passwordInput.type = isPassword ? 'text' : 'password';

            // Update icon
            eyeIcon.innerHTML = isPassword
                ? `<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21"></path>`
                : `<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>`;
        });
    }

    setupPasswordStrength() {
        const passwordInput = document.getElementById('password');
        passwordInput.addEventListener('input', () => this.updatePasswordStrength());
    }

    updatePasswordStrength() {
        const password = document.getElementById('password').value;
        const strengthBar = document.getElementById('strengthBar');
        const strengthText = document.getElementById('strengthText');

        if (!password) {
            strengthBar.style.width = '0%';
            strengthText.textContent = '-';
            strengthText.className = '';
            return;
        }

        let score = 0;
        let feedback = [];

        // Length check
        if (password.length >= 8) score += 2;
        else if (password.length >= 6) score += 1;
        else feedback.push('at least 6 characters');

        // Character variety checks
        if (/[a-z]/.test(password)) score += 1;
        else feedback.push('lowercase letters');

        if (/[A-Z]/.test(password)) score += 1;
        else feedback.push('uppercase letters');

        if (/[0-9]/.test(password)) score += 1;
        else feedback.push('numbers');

        if (/[^A-Za-z0-9]/.test(password)) score += 1;
        else feedback.push('special characters');

        // Update UI based on score
        let strength, color, width;

        if (score <= 2) {
            strength = 'Weak';
            color = '#ef4444';
            width = '25%';
            strengthText.className = 'strength-weak';
        } else if (score <= 4) {
            strength = 'Medium';
            color = '#f59e0b';
            width = '60%';
            strengthText.className = 'strength-medium';
        } else {
            strength = 'Strong';
            color = '#10b981';
            width = '100%';
            strengthText.className = 'strength-strong';
        }

        strengthBar.style.width = width;
        strengthBar.style.backgroundColor = color;
        strengthText.textContent = strength;
    }

    async checkUsernameAvailability() {
        const username = document.getElementById('username').value.trim();

        if (username.length < 3) return;

        try {
            // In frontend-only mode, check localStorage
            const users = this.loadUsers();
            const exists = users.users.some(user => {
                if (!user || !user.username) {
                    console.warn('Invalid user object found during username check:', user);
                    return false;
                }
                return user.username.toLowerCase() === username.toLowerCase();
            });

            if (exists) {
                this.showError('username', 'Username is already taken');
            }
        } catch (error) {
            console.error('Error checking username:', error);
        }
    }

    async handleSubmit(e) {
        e.preventDefault();

        const username = document.getElementById('username').value.trim();
        const email = document.getElementById('email').value.trim();
        const password = document.getElementById('password').value;
        const confirmPassword = document.getElementById('confirmPassword').value;
        const termsAccepted = document.getElementById('terms').checked;

        // Clear previous errors
        this.clearAllErrors();

        // Validate all fields individually with detailed logging
        console.log('Starting validation...');

        const usernameValid = this.validateUsername();
        console.log('Username validation:', usernameValid);

        const emailValid = this.validateEmail();
        console.log('Email validation:', emailValid);

        const passwordValid = this.validatePassword();
        console.log('Password validation:', passwordValid);

        const passwordMatchValid = this.validatePasswordMatch();
        console.log('Password match validation:', passwordMatchValid);

        const termsValid = this.validateTerms();
        console.log('Terms validation:', termsValid);

        if (!usernameValid || !emailValid || !passwordValid || !passwordMatchValid || !termsValid) {
            this.showToast('Please fix the errors above', 'error');
            this.setLoadingState(false);
            return;
        }

        console.log('All validations passed!');

        // Show loading state
        this.setLoadingState(true);

        try {
            console.log('Starting registration process...');

            // Simulate API delay
            await this.delay(1000);

            // Check if username/email already exists
            const users = this.loadUsers();
            console.log('Loaded users:', users);

            // Check for duplicates with proper error handling
            const usernameExists = users.users.some(u => {
                if (!u || !u.username) {
                    console.warn('Invalid user object found:', u);
                    return false;
                }
                return u.username.toLowerCase() === username.toLowerCase();
            });

            if (usernameExists) {
                this.setLoadingState(false);
                this.showError('username', 'Username is already taken');
                this.showToast('Username already exists', 'error');
                return;
            }

            const emailExists = users.users.some(u => {
                if (!u || !u.email) {
                    console.warn('Invalid user object found:', u);
                    return false;
                }
                return u.email.toLowerCase() === email.toLowerCase();
            });

            if (emailExists) {
                this.setLoadingState(false);
                this.showError('email', 'Email is already registered');
                this.showToast('Email already registered', 'error');
                return;
            }

            // Create new user
            console.log('Creating user:', { username, email });
            await this.createUser(username, email, password);
            console.log('User created successfully!');

            this.showToast('Account created successfully! Redirecting...', 'success');

            // Redirect to login page
            setTimeout(() => {
                window.location.href = 'login.html?registered=true&username=' + encodeURIComponent(username);
            }, 2000);

        } catch (error) {
            console.error('Registration error:', error);
            this.showToast('Something went wrong. Please try again.', 'error');
        } finally {
            this.setLoadingState(false);
        }
    }

    validateUsername() {
        const username = document.getElementById('username').value.trim();
        const usernameRegex = /^[a-zA-Z0-9_]+$/;

        if (!username) {
            this.showError('username', 'Username is required');
            return false;
        }

        if (username.length < 3) {
            this.showError('username', 'Username must be at least 3 characters');
            return false;
        }

        if (username.length > 20) {
            this.showError('username', 'Username must be less than 20 characters');
            return false;
        }

        if (!usernameRegex.test(username)) {
            this.showError('username', 'Username can only contain letters, numbers, and underscores');
            return false;
        }

        return true;
    }

    validateEmail() {
        const email = document.getElementById('email').value.trim();
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

        if (!email) {
            this.showError('email', 'Email is required');
            return false;
        }

        if (!emailRegex.test(email)) {
            this.showError('email', 'Please enter a valid email address');
            return false;
        }

        return true;
    }

    validatePassword() {
        const password = document.getElementById('password').value;

        if (!password) {
            this.showError('password', 'Password is required');
            return false;
        }

        if (password.length < 6) {
            this.showError('password', 'Password must be at least 6 characters long');
            return false;
        }

        return true;
    }

    validatePasswordMatch() {
        const password = document.getElementById('password').value;
        const confirmPassword = document.getElementById('confirmPassword').value;

        if (!confirmPassword) {
            this.showError('confirmPassword', 'Please confirm your password');
            return false;
        }

        if (password !== confirmPassword) {
            this.showError('confirmPassword', 'Passwords do not match');
            return false;
        }

        return true;
    }

    validateTerms() {
        const termsCheckbox = document.getElementById('terms');
        console.log('Terms checkbox element:', termsCheckbox);
        console.log('Terms checkbox checked:', termsCheckbox ? termsCheckbox.checked : 'element not found');

        if (!termsCheckbox) {
            console.error('Terms checkbox not found!');
            return false;
        }

        const termsAccepted = termsCheckbox.checked;

        if (!termsAccepted) {
            this.showError('terms', 'You must accept the terms and conditions');
            return false;
        }

        return true;
    }

    showError(field, message) {
        try {
            const errorElement = document.getElementById(`${field}Error`);
            const inputElement = document.getElementById(field);

            if (!errorElement) {
                console.error(`Error element not found: ${field}Error`);
                return;
            }

            errorElement.textContent = message;
            errorElement.classList.remove('hidden');

            if (inputElement && inputElement.type !== 'checkbox') {
                inputElement.classList.add('border-red-500');
            }

            console.log(`Showing error for ${field}: ${message}`);
        } catch (error) {
            console.error('Error in showError:', error);
        }
    }

    clearError(field) {
        const errorElement = document.getElementById(`${field}Error`);
        const inputElement = document.getElementById(field);

        errorElement.classList.add('hidden');

        if (inputElement && inputElement.type !== 'checkbox') {
            inputElement.classList.remove('border-red-500');
        }
    }

    clearAllErrors() {
        ['username', 'email', 'password', 'confirmPassword', 'terms'].forEach(field => this.clearError(field));
    }

    setLoadingState(loading) {
        try {
            const registerBtn = document.getElementById('registerBtn');
            const registerText = document.getElementById('registerText');
            const registerSpinner = document.getElementById('registerSpinner');

            if (!registerBtn || !registerText || !registerSpinner) {
                console.error('Loading state elements not found');
                return;
            }

            if (loading) {
                registerBtn.disabled = true;
                registerBtn.classList.add('opacity-75', 'cursor-not-allowed');
                registerText.classList.add('hidden');
                registerSpinner.classList.remove('hidden');
                console.log('Loading state: ON');
            } else {
                registerBtn.disabled = false;
                registerBtn.classList.remove('opacity-75', 'cursor-not-allowed');
                registerText.classList.remove('hidden');
                registerSpinner.classList.add('hidden');
                console.log('Loading state: OFF');
            }
        } catch (error) {
            console.error('Error in setLoadingState:', error);
        }
    }

    loadUsers() {
        try {
            const usersData = localStorage.getItem('users');
            if (!usersData) {
                return { users: [] };
            }

            const parsed = JSON.parse(usersData);

            // Ensure the structure is valid
            if (!parsed || !Array.isArray(parsed.users)) {
                console.warn('Invalid users data structure, resetting...');
                return { users: [] };
            }

            // Filter out any invalid user objects
            const validUsers = parsed.users.filter(user => {
                if (!user || typeof user !== 'object') {
                    console.warn('Removing invalid user object:', user);
                    return false;
                }
                if (!user.username || !user.email) {
                    console.warn('Removing user with missing username/email:', user);
                    return false;
                }
                return true;
            });

            // If we filtered out any users, save the cleaned data
            if (validUsers.length !== parsed.users.length) {
                console.log(`Cleaned users data: ${parsed.users.length} -> ${validUsers.length}`);
                const cleanedData = { users: validUsers };
                localStorage.setItem('users', JSON.stringify(cleanedData));
                return cleanedData;
            }

            return parsed;
        } catch (error) {
            console.error('Error loading users:', error);
            // Reset to empty if there's a parsing error
            localStorage.removeItem('users');
            return { users: [] };
        }
    }

    saveUsers(users) {
        try {
            localStorage.setItem('users', JSON.stringify(users));
        } catch (error) {
            console.error('Error saving users:', error);
        }
    }

    async createUser(username, email, password) {
        try {
            const users = this.loadUsers();
            console.log('Current users before adding:', users);

            // Create new user object
            const newUser = {
                id: Date.now(),
                username: username.toLowerCase(),
                email: email.toLowerCase(),
                password: password, // In real app, hash this!
                createdAt: new Date().toISOString(),
                lastLogin: null
            };

            // Add to users list
            users.users.push(newUser);
            console.log('Users after adding new user:', users);

            this.saveUsers(users);
            console.log('Users saved to localStorage');

            // Create individual user data file (simulated in localStorage)
            const userData = {
                username: username.toLowerCase(),
                memory: {},
                savedChats: [],
                settings: {
                    theme: 'dark',
                    notifications: true,
                    language: 'en'
                },
                createdAt: new Date().toISOString(),
                lastLogin: null
            };

            // Save user-specific data
            localStorage.setItem(`user_${username.toLowerCase()}`, JSON.stringify(userData));
            console.log('User data saved:', userData);

            console.log('New user created:', { username: newUser.username, id: newUser.id });
            return newUser;
        } catch (error) {
            console.error('Error in createUser:', error);
            throw error;
        }
    }

    showToast(message, type = 'success') {
        const toast = document.getElementById('toast');
        const toastMessage = document.getElementById('toastMessage');

        // Update message
        toastMessage.textContent = message;

        // Update colors based on type
        const toastDiv = toast.querySelector('div');
        toastDiv.className = `px-6 py-3 rounded-lg shadow-lg flex items-center ${
            type === 'success' ? 'bg-green-500 text-white' :
            type === 'error' ? 'bg-red-500 text-white' :
            type === 'info' ? 'bg-blue-500 text-white' :
            'bg-gray-500 text-white'
        }`;

        // Show toast
        toast.classList.remove('hidden');

        // Auto hide after 4 seconds
        setTimeout(() => {
            toast.classList.add('hidden');
        }, 4000);
    }

    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

// Initialize the registration manager when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    // Clear any corrupted data first
    try {
        const usersData = localStorage.getItem('users');
        if (usersData) {
            const parsed = JSON.parse(usersData);
            console.log('Current users data:', parsed);
        }
    } catch (error) {
        console.warn('Corrupted users data found, clearing...', error);
        localStorage.removeItem('users');
    }

    window.registrationManager = new RegistrationManager();

    // Add debug functions to window for testing
    window.debugRegistration = {
        clearAllData: () => {
            localStorage.removeItem('users');
            for (let i = localStorage.length - 1; i >= 0; i--) {
                const key = localStorage.key(i);
                if (key && key.startsWith('user_')) {
                    localStorage.removeItem(key);
                }
            }
            console.log('🗑️ All registration data cleared');
        },
        viewUsers: () => {
            const users = localStorage.getItem('users');
            console.log('👥 Users data:', users ? JSON.parse(users) : 'No users found');
        },
        viewUserData: (username) => {
            const userData = localStorage.getItem(`user_${username.toLowerCase()}`);
            console.log(`👤 User data for ${username}:`, userData ? JSON.parse(userData) : 'User not found');
        }
    };

    console.log('🚀 Registration Manager initialized!');
    console.log('🧹 Data validation complete!');
    console.log('🛠️ Debug commands available:');
    console.log('  debugRegistration.clearAllData() - Clear all data');
    console.log('  debugRegistration.viewUsers() - View all users');
    console.log('  debugRegistration.viewUserData("username") - View specific user data');
});
