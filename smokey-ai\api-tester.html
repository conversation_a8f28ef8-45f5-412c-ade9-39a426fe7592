<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API Key Tester - Smokey AI</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
    </style>
</head>
<body class="flex items-center justify-center p-4">
    <div class="bg-white rounded-2xl p-8 shadow-2xl w-full max-w-2xl">
        <div class="text-center mb-6">
            <h1 class="text-2xl font-bold text-gray-800 mb-2">🧪 API Key Tester</h1>
            <p class="text-gray-600">Test your API key with different providers</p>
        </div>

        <div class="space-y-6">
            <!-- API Key Display -->
            <div class="bg-gray-50 p-4 rounded-lg">
                <h3 class="font-semibold text-gray-700 mb-2">Your API Key:</h3>
                <code class="text-sm text-purple-600 break-all">f7c22b80-3d79-11f0-b7b2-31db9926ded4</code>
            </div>

            <!-- Test Buttons -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <button onclick="testProvider('segmind')" class="test-btn bg-blue-500 hover:bg-blue-600 text-white p-4 rounded-lg transition-colors">
                    <div class="font-semibold">Test Segmind API</div>
                    <div class="text-sm opacity-90">Stable Diffusion models</div>
                </button>

                <button onclick="testProvider('replicate')" class="test-btn bg-green-500 hover:bg-green-600 text-white p-4 rounded-lg transition-colors">
                    <div class="font-semibold">Test Replicate API</div>
                    <div class="text-sm opacity-90">Various AI models</div>
                </button>

                <button onclick="testProvider('huggingface')" class="test-btn bg-yellow-500 hover:bg-yellow-600 text-white p-4 rounded-lg transition-colors">
                    <div class="font-semibold">Test Hugging Face</div>
                    <div class="text-sm opacity-90">Open source models</div>
                </button>

                <button onclick="testProvider('custom')" class="test-btn bg-purple-500 hover:bg-purple-600 text-white p-4 rounded-lg transition-colors">
                    <div class="font-semibold">Test Custom API</div>
                    <div class="text-sm opacity-90">Generic endpoint</div>
                </button>
            </div>

            <!-- Results -->
            <div id="results" class="hidden">
                <h3 class="font-semibold text-gray-700 mb-3">Test Results:</h3>
                <div id="resultContent" class="bg-gray-50 p-4 rounded-lg"></div>
            </div>

            <!-- Generated Image -->
            <div id="imageResult" class="hidden">
                <h3 class="font-semibold text-gray-700 mb-3">Generated Image:</h3>
                <img id="generatedImage" class="w-full max-w-md mx-auto rounded-lg shadow-lg" alt="Generated image">
            </div>
        </div>
    </div>

    <script>
        const API_KEY = 'f7c22b80-3d79-11f0-b7b2-31db9926ded4';
        
        async function testProvider(provider) {
            const resultsDiv = document.getElementById('results');
            const resultContent = document.getElementById('resultContent');
            const imageResult = document.getElementById('imageResult');
            const generatedImage = document.getElementById('generatedImage');
            
            resultsDiv.classList.remove('hidden');
            imageResult.classList.add('hidden');
            resultContent.innerHTML = `<div class="text-blue-600">🧪 Testing ${provider} API...</div>`;
            
            try {
                let result;
                
                switch (provider) {
                    case 'segmind':
                        result = await testSegmindAPI();
                        break;
                    case 'replicate':
                        result = await testReplicateAPI();
                        break;
                    case 'huggingface':
                        result = await testHuggingFaceAPI();
                        break;
                    case 'custom':
                        result = await testCustomAPI();
                        break;
                    default:
                        throw new Error('Unknown provider');
                }
                
                resultContent.innerHTML = `
                    <div class="text-green-600 font-semibold">✅ ${provider} API - SUCCESS!</div>
                    <div class="text-sm text-gray-600 mt-2">Response: ${JSON.stringify(result, null, 2)}</div>
                `;
                
                if (result.imageUrl) {
                    generatedImage.src = result.imageUrl;
                    imageResult.classList.remove('hidden');
                }
                
            } catch (error) {
                resultContent.innerHTML = `
                    <div class="text-red-600 font-semibold">❌ ${provider} API - FAILED</div>
                    <div class="text-sm text-gray-600 mt-2">Error: ${error.message}</div>
                `;
                console.error(`${provider} test failed:`, error);
            }
        }
        
        async function testSegmindAPI() {
            const response = await fetch('https://api.segmind.com/v1/sd1.5-txt2img', {
                method: 'POST',
                headers: {
                    'x-api-key': API_KEY,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    prompt: 'a cute cat sitting in a garden, digital art',
                    negative_prompt: 'blurry, bad quality',
                    style: 'base',
                    samples: 1,
                    scheduler: 'UniPC',
                    num_inference_steps: 25,
                    guidance_scale: 8,
                    seed: 12345,
                    img_width: 512,
                    img_height: 512
                })
            });
            
            if (!response.ok) {
                const errorText = await response.text();
                throw new Error(`HTTP ${response.status}: ${errorText}`);
            }
            
            const data = await response.json();
            return { provider: 'segmind', imageUrl: data.image || data.url, data };
        }
        
        async function testReplicateAPI() {
            const response = await fetch('https://api.replicate.com/v1/predictions', {
                method: 'POST',
                headers: {
                    'Authorization': `Token ${API_KEY}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    version: 'ac732df83cea7fff18b8472768c88ad041fa750ff7682a21affe81863cbe77e4',
                    input: {
                        prompt: 'a cute cat sitting in a garden, digital art',
                        width: 512,
                        height: 512,
                        num_inference_steps: 25
                    }
                })
            });
            
            if (!response.ok) {
                const errorText = await response.text();
                throw new Error(`HTTP ${response.status}: ${errorText}`);
            }
            
            const data = await response.json();
            return { provider: 'replicate', data };
        }
        
        async function testHuggingFaceAPI() {
            const response = await fetch('https://api-inference.huggingface.co/models/stabilityai/stable-diffusion-xl-base-1.0', {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${API_KEY}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    inputs: 'a cute cat sitting in a garden, digital art'
                })
            });
            
            if (!response.ok) {
                const errorText = await response.text();
                throw new Error(`HTTP ${response.status}: ${errorText}`);
            }
            
            const blob = await response.blob();
            const imageUrl = URL.createObjectURL(blob);
            return { provider: 'huggingface', imageUrl, data: 'Binary image data' };
        }
        
        async function testCustomAPI() {
            // Test a generic API endpoint
            const response = await fetch('https://httpbin.org/post', {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${API_KEY}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    test: 'API key validation',
                    key: API_KEY
                })
            });
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}`);
            }
            
            const data = await response.json();
            return { provider: 'custom', data };
        }
    </script>
</body>
</html>
