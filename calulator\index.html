<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VisiCalc</title>
    <link rel="stylesheet" href="style.css">
    <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700&display=swap" rel="stylesheet">
    <style>
        html {
            scroll-behavior: smooth;
        }
    </style>
</head>
<body>
    <div class="landing-page">
        <div class="landing-content">
            <h1 class="landing-title">VisiCalc</h1>
            <p class="landing-subtitle">Calculate with Style</p>
            <a href="#calculator" class="scroll-down">
                <svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-arrow-down">
                    <line x1="12" y1="5" x2="12" y2="19"></line>
                    <polyline points="19 12 12 19 5 12"></polyline>
                </svg>
            </a>
        </div>
    </div>

    <div class="calculator" id="calculator">
        <div class="title">VisiCalc</div>
        <div class="display">
            <input type="text" id="result" readonly>
        </div>
        <div class="buttons">
            <button class="operator" data-value="AC">AC</button>
            <button class="operator" data-value="C">C</button>
            <button class="operator" data-value="%">%</button>
            <button class="operator" data-value="/">/</button>
            <button data-value="7">7</button>
            <button data-value="8">8</button>
            <button data-value="9">9</button>
            <button class="operator" data-value="*">*</button>
            <button data-value="4">4</button>
            <button data-value="5">5</button>
            <button data-value="6">6</button>
            <button class="operator" data-value="-">-</button>
            <button data-value="1">1</button>
            <button data-value="2">2</button>
            <button data-value="3">3</button>
            <button class="operator" data-value="+">+</button>
            <button data-value="0">0</button>
            <button data-value=".">.</button>
            <button class="equals" data-value="=">=</button>
        </div>
        <div class="toggle">
            <input type="checkbox" id="dark-mode">
            <label for="dark-mode">Dark Mode</label>
        </div>
    </div>
    <script src="script.js"></script>
</body>
</html>
