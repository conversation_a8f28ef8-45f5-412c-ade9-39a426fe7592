// main.js

/*
// Scene
const scene = new THREE.Scene();

// Camera
const camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);

// Renderer
const renderer = new THREE.WebGLRenderer();
renderer.setSize(window.innerWidth, window.innerHeight);
renderer.shadowMap.enabled = true; // Enable shadows in the renderer
document.body.appendChild(renderer.domElement);

// Car
const carGeometry = new THREE.BoxGeometry(1, 0.5, 2);
const carMaterial = new THREE.MeshLambertMaterial({ color: 0xff0000 });
const car = new THREE.Mesh(carGeometry, carMaterial);
car.position.y = 0.25; // Adjust car position to be on the ground
scene.add(car);

// Ground
const groundGeometry = new THREE.PlaneGeometry(100, 100, 100, 100); // Add segments for vertex colors
const groundMaterial = new THREE.MeshLambertMaterial({ vertexColors: true });
const ground = new THREE.Mesh(groundGeometry, groundMaterial);
ground.rotation.x = -Math.PI / 2; // Rotate to be horizontal
scene.add(ground);

// Add random vertex colors to the ground
const groundColors = [];
for (let i = 0; i < groundGeometry.attributes.position.count; i++) {
    const color = new THREE.Color(0x00ff00);
    color.r += Math.random() * 0.1 - 0.05;
    color.g += Math.random() * 0.1 - 0.05;
    groundColors.push(color.r, color.g, color.b);
}
groundGeometry.setAttribute('color', new THREE.Float32BufferAttribute(groundColors, 3));

// Lighting
const ambientLight = new THREE.AmbientLight(0x404040); // Soft white light
scene.add(ambientLight);

const directionalLight = new THREE.DirectionalLight(0xffffff, 0.5);
directionalLight.position.set(1, 1, 1);
directionalLight.castShadow = true; // Enable shadows
scene.add(directionalLight);

// Set shadow properties
directionalLight.shadow.mapSize.width = 2048;
directionalLight.shadow.mapSize.height = 2048;
directionalLight.shadow.camera.near = 0.5;
directionalLight.shadow.camera.far = 50;

// Road
const roadGeometry = new THREE.PlaneGeometry(5, 200);
const roadMaterial = new THREE.MeshLambertMaterial({ color: 0x404040 });
const road = new THREE.Mesh(roadGeometry, roadMaterial);
road.rotation.x = -Math.PI / 2;
road.position.y = 0.01; // Adjust position to be on top of the ground
road.castShadow = true;
road.receiveShadow = true;
scene.add(road);

// Environment
for (let i = 0; i < 10; i++) {
    const buildingHeight = Math.random() * 20 + 5;
    const buildingGeometry = new THREE.BoxGeometry(5, buildingHeight, 5);
    const buildingMaterial = new THREE.MeshLambertMaterial({ color: Math.random() * 0xffffff });
    const building = new THREE.Mesh(buildingGeometry, buildingMaterial);
    building.position.x = Math.random() * 100 - 50;
    building.position.z = Math.random() * 100 - 50;
    building.position.y = buildingHeight / 2; // Adjust position to be on the ground
    building.castShadow = true; // Enable shadows for buildings
    scene.add(building);
}

// Enable shadow receiving for ground
ground.receiveShadow = true;

// Car properties
let carSpeed = 0;
const maxSpeed = 0.5;
const acceleration = 0.01;
const deceleration = 0.02;
const turnSpeed = 0.05;

camera.position.y = 20;
camera.position.z = 40;
camera.lookAt(car.position);

// Car controls
document.addEventListener('keydown', function(event) {
    switch(event.keyCode) {
        case 37: // Left arrow
            car.rotation.y += turnSpeed;
            break;
        case 39: // Right arrow
            car.rotation.y -= turnSpeed;
            break;
        case 38: // Up arrow
            carSpeed = Math.min(carSpeed + acceleration, maxSpeed);
            break;
        case 40: // Down arrow
            carSpeed = Math.max(carSpeed - acceleration, -maxSpeed);
            break;
    }
});

let friction = 0.01;
document.addEventListener('keyup', function(event) {
    if (event.keyCode === 38 || event.keyCode === 40) {
        // Decelerate when up/down arrow is released
        friction = 0.01;
    }
});

// Animation loop
function animate() {
    requestAnimationFrame(animate);

    // Update car position based on speed and rotation
    car.position.x += carSpeed * Math.sin(car.rotation.y);
    car.position.z += carSpeed * Math.cos(car.rotation.y);

    // Apply friction
    carSpeed *= (1 - friction);

    // Smooth camera movement
    const cameraLag = 0.1;
    camera.position.x += (car.position.x - camera.position.x) * cameraLag;
    camera.position.y += (car.position.y + 5 - camera.position.y) * cameraLag;
    camera.position.z += (car.position.z + 10 - car.position.z) * cameraLag;
    camera.lookAt(car.position);

    renderer.render(scene, camera);
}

animate();
*/

// -----------------------------------------------------------------------------
// New code for landing page and game initialization
// -----------------------------------------------------------------------------

const landingPage = document.querySelector('.landing-page');
const startButton = document.getElementById('start-button');
const gameContainer = document.createElement('div');
document.body.appendChild(gameContainer);

// Scene
const scene = new THREE.Scene();

// Camera
const camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
camera.position.y = 20;
camera.position.z = 40;

// Renderer
const renderer = new THREE.WebGLRenderer();
renderer.setSize(window.innerWidth, window.innerHeight);
renderer.shadowMap.enabled = true; // Enable shadows in the renderer
gameContainer.appendChild(renderer.domElement);

// Car
const carGeometry = new THREE.BoxGeometry(1, 0.5, 2);
const carMaterial = new THREE.MeshLambertMaterial({ color: 0xff0000 });
const car = new THREE.Mesh(carGeometry, carMaterial);
car.position.y = 0.25; // Adjust car position to be on the ground
scene.add(car);

// Ground
const groundGeometry = new THREE.PlaneGeometry(100, 100, 100, 100); // Add segments for vertex colors
const groundMaterial = new THREE.MeshLambertMaterial({ vertexColors: true });
const ground = new THREE.Mesh(groundGeometry, groundMaterial);
ground.rotation.x = -Math.PI / 2; // Rotate to be horizontal
scene.add(ground);

// Add random vertex colors to the ground
const groundColors = [];
for (let i = 0; i < groundGeometry.attributes.position.count; i++) {
    const color = new THREE.Color(0x00ff00);
    color.r += Math.random() * 0.1 - 0.05;
    color.g += Math.random() * 0.1 - 0.05;
    groundColors.push(color.r, color.g, color.b);
}
groundGeometry.setAttribute('color', new THREE.Float32BufferAttribute(groundColors, 3));

// Lighting
const ambientLight = new THREE.AmbientLight(0x404040); // Soft white light
scene.add(ambientLight);

const directionalLight = new THREE.DirectionalLight(0xffffff, 0.5);
directionalLight.position.set(1, 1, 1);
directionalLight.castShadow = true; // Enable shadows
scene.add(directionalLight);

// Set shadow properties
directionalLight.shadow.mapSize.width = 2048;
directionalLight.shadow.mapSize.height = 2048;
directionalLight.shadow.camera.near = 0.5;
directionalLight.shadow.camera.far = 50;

// Road
const roadGeometry = new THREE.PlaneGeometry(5, 200);
const roadMaterial = new THREE.MeshLambertMaterial({ color: 0x404040 });
const road = new THREE.Mesh(roadGeometry, roadMaterial);
road.rotation.x = -Math.PI / 2;
road.position.y = 0.01; // Adjust position to be on top of the ground
road.castShadow = true;
road.receiveShadow = true;
scene.add(road);

// Environment
for (let i = 0; i < 10; i++) {
    const buildingHeight = Math.random() * 20 + 5;
    const buildingGeometry = new THREE.BoxGeometry(5, buildingHeight, 5);
    const buildingMaterial = new THREE.MeshLambertMaterial({ color: Math.random() * 0xffffff });
    const building = new THREE.Mesh(buildingGeometry, buildingMaterial);
    building.position.x = Math.random() * 100 - 50;
    building.position.z = Math.random() * 100 - 50;
    building.position.y = buildingHeight / 2; // Adjust position to be on the ground
    building.castShadow = true; // Enable shadows for buildings
    scene.add(building);
}

// Enable shadow receiving for ground
ground.receiveShadow = true;

// Car properties
let carSpeed = 0;
const maxSpeed = 0.5;
const acceleration = 0.01;
const deceleration = 0.02;
const turnSpeed = 0.05;

let friction = 0.01;

// Car controls
document.addEventListener('keydown', function(event) {
    switch(event.keyCode) {
        case 37: // Left arrow
            car.rotation.y += turnSpeed;
            break;
        case 39: // Right arrow
            car.rotation.y -= turnSpeed;
            break;
        case 38: // Up arrow
            carSpeed = Math.min(carSpeed + acceleration, maxSpeed);
            break;
        case 40: // Down arrow
            carSpeed = Math.max(carSpeed - acceleration, -maxSpeed);
            break;
    }
});

document.addEventListener('keyup', function(event) {
    if (event.keyCode === 38 || event.keyCode === 40) {
        // Decelerate when up/down arrow is released
        friction = 0.01;
    }
});

// Animation loop
function animate() {
    requestAnimationFrame(animate);

    // Update car position based on speed and rotation
    car.position.x += carSpeed * Math.sin(car.rotation.y);
    car.position.z += carSpeed * Math.cos(car.rotation.y);

    // Apply friction
    carSpeed *= (1 - friction);

    // Smooth camera movement
    const cameraLag = 0.1;
    camera.position.x += (car.position.x - camera.position.x) * cameraLag;
    camera.position.y += (car.position.y + 5 - camera.position.y) * cameraLag;
    camera.position.z += (car.position.z + 10 - camera.position.z) * cameraLag;
    camera.lookAt(car.position);

    renderer.render(scene, camera);
}

function startGame() {
    landingPage.style.display = 'none';
    gameContainer.style.width = '100%';
    gameContainer.style.height = '100vh';
    animate();
}

startButton.addEventListener('click', startGame);
