// Smokey AI v1 Configuration
// This file contains API keys and configuration settings

const CONFIG = {
    // Image Generation API Settings
    imageGeneration: {
        // Your API key
        apiKey: 'f7c22b80-3d79-11f0-b7b2-31db9926ded4',

        // API Provider (options: 'openai', 'stability', 'deepimage', 'custom')
        provider: 'deepimage',

        // API Endpoints
        endpoints: {
            openai: 'https://api.openai.com/v1/images/generations',
            stability: 'https://api.stability.ai/v1/generation/stable-diffusion-xl-1024-v1-0/text-to-image',
            deepai: 'https://api.deepai.org/api/text2img',
            deepimage: 'https://api.deepimage.ai/v1/generate', // Generic endpoint - will be updated
            replicate: 'https://api.replicate.com/v1/predictions',
            huggingface: 'https://api-inference.huggingface.co/models/stabilityai/stable-diffusion-xl-base-1.0',
            custom: 'https://api.segmind.com/v1/sd1.5-txt2img'
        },

        // Default settings
        defaults: {
            model: 'dall-e-3',
            size: '1024x1024',
            quality: 'standard',
            style: 'realistic'
        },

        // Rate limiting
        rateLimit: {
            maxRequestsPerMinute: 5,
            maxRequestsPerHour: 50
        }
    },

    // Chat API Settings (for future use)
    chat: {
        apiKey: 'YOUR_CHAT_API_KEY_HERE',
        provider: 'openai',
        model: 'gpt-3.5-turbo',
        maxTokens: 2048
    },

    // Application Settings
    app: {
        version: 'v1',
        name: 'Smokey AI',
        maxChatHistory: 100,
        maxMemoryItems: 500,
        autoSave: true,
        debugMode: false
    },

    // UI Settings
    ui: {
        theme: 'dark',
        animations: true,
        soundEffects: false,
        notifications: true
    }
};

// API Key validation
function validateApiKey(key) {
    if (!key || key === 'YOUR_API_KEY_HERE') {
        console.warn('⚠️ API key not configured. Please set your API key in config.js');
        return false;
    }

    if (key.length < 10) {
        console.warn('⚠️ API key appears to be invalid (too short)');
        return false;
    }

    return true;
}

// Get configuration with validation
function getConfig() {
    // Validate image generation API key
    if (!validateApiKey(CONFIG.imageGeneration.apiKey)) {
        CONFIG.imageGeneration.enabled = false;
    } else {
        CONFIG.imageGeneration.enabled = true;
    }

    return CONFIG;
}

// Export for use in other files
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { CONFIG, getConfig, validateApiKey };
} else {
    window.CONFIG = CONFIG;
    window.getConfig = getConfig;
    window.validateApiKey = validateApiKey;
}

console.log('🔧 Configuration loaded:', {
    version: CONFIG.app.version,
    imageGeneration: CONFIG.imageGeneration.enabled ? 'enabled' : 'disabled',
    provider: CONFIG.imageGeneration.provider
});
