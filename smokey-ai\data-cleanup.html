<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Data Cleanup Tool - Smokey AI v1</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
    </style>
</head>
<body class="flex items-center justify-center p-4">
    <div class="bg-white rounded-2xl p-8 shadow-2xl w-full max-w-2xl">
        <div class="text-center mb-6">
            <h1 class="text-2xl font-bold text-gray-800 mb-2">🧹 Data Cleanup Tool</h1>
            <p class="text-gray-600">Reset Smokey AI v1 to fresh state</p>
        </div>

        <!-- Current Data Status -->
        <div class="bg-gray-50 p-4 rounded-lg mb-6">
            <h3 class="font-semibold text-gray-700 mb-3">📊 Current Data Status:</h3>
            <div id="dataStatus" class="space-y-2 text-sm">
                <div>🔄 Loading data status...</div>
            </div>
        </div>

        <!-- Cleanup Options -->
        <div class="space-y-4 mb-6">
            <h3 class="font-semibold text-gray-700">🗑️ Cleanup Options:</h3>
            
            <div class="space-y-3">
                <button onclick="clearAllUsers()" class="w-full bg-red-500 hover:bg-red-600 text-white p-3 rounded-lg transition-colors flex items-center justify-center gap-2">
                    <span>👥</span>
                    <span>Clear All User Accounts</span>
                </button>

                <button onclick="clearAllChats()" class="w-full bg-orange-500 hover:bg-orange-600 text-white p-3 rounded-lg transition-colors flex items-center justify-center gap-2">
                    <span>💬</span>
                    <span>Clear All Chat History</span>
                </button>

                <button onclick="clearAllMemory()" class="w-full bg-yellow-500 hover:bg-yellow-600 text-white p-3 rounded-lg transition-colors flex items-center justify-center gap-2">
                    <span>🧠</span>
                    <span>Clear All Memory Data</span>
                </button>

                <button onclick="clearAllSessions()" class="w-full bg-blue-500 hover:bg-blue-600 text-white p-3 rounded-lg transition-colors flex items-center justify-center gap-2">
                    <span>🔐</span>
                    <span>Clear All Login Sessions</span>
                </button>

                <button onclick="clearEverything()" class="w-full bg-purple-600 hover:bg-purple-700 text-white p-4 rounded-lg transition-colors flex items-center justify-center gap-2 font-semibold">
                    <span>🚨</span>
                    <span>RESET EVERYTHING (Complete Wipe)</span>
                </button>
            </div>
        </div>

        <!-- Results -->
        <div id="results" class="hidden">
            <h3 class="font-semibold text-gray-700 mb-3">✅ Cleanup Results:</h3>
            <div id="resultContent" class="bg-gray-50 p-4 rounded-lg text-sm"></div>
        </div>

        <!-- Refresh Button -->
        <div class="mt-6 text-center">
            <button onclick="refreshStatus()" class="bg-gray-500 hover:bg-gray-600 text-white px-6 py-2 rounded-lg transition-colors">
                🔄 Refresh Status
            </button>
        </div>
    </div>

    <script>
        // Load and display current data status
        function refreshStatus() {
            const statusDiv = document.getElementById('dataStatus');
            
            try {
                // Count users
                const usersData = localStorage.getItem('users');
                const users = usersData ? JSON.parse(usersData) : { users: [] };
                const userCount = users.users ? users.users.length : 0;

                // Count user data files
                let userDataFiles = 0;
                let totalChats = 0;
                let totalMemoryItems = 0;

                for (let i = 0; i < localStorage.length; i++) {
                    const key = localStorage.key(i);
                    if (key && key.startsWith('user_')) {
                        userDataFiles++;
                        try {
                            const userData = JSON.parse(localStorage.getItem(key));
                            if (userData.savedChats) {
                                totalChats += userData.savedChats.length;
                            }
                            if (userData.memory) {
                                totalMemoryItems += Object.keys(userData.memory).length;
                            }
                        } catch (e) {
                            console.warn('Invalid user data file:', key);
                        }
                    }
                }

                // Count sessions
                const currentSession = localStorage.getItem('currentSession') || sessionStorage.getItem('currentSession');
                const rememberedUser = localStorage.getItem('rememberedUser');
                const sessionCount = (currentSession ? 1 : 0) + (rememberedUser ? 1 : 0);

                // Count total localStorage items
                const totalItems = localStorage.length;

                statusDiv.innerHTML = `
                    <div class="grid grid-cols-2 gap-4">
                        <div>👥 <strong>User Accounts:</strong> ${userCount}</div>
                        <div>📁 <strong>User Data Files:</strong> ${userDataFiles}</div>
                        <div>💬 <strong>Total Chats:</strong> ${totalChats}</div>
                        <div>🧠 <strong>Memory Items:</strong> ${totalMemoryItems}</div>
                        <div>🔐 <strong>Active Sessions:</strong> ${sessionCount}</div>
                        <div>📦 <strong>Total Storage Items:</strong> ${totalItems}</div>
                    </div>
                    <div class="mt-3 text-xs text-gray-500">
                        <strong>Storage Usage:</strong> ~${Math.round(JSON.stringify(localStorage).length / 1024)} KB
                    </div>
                `;

            } catch (error) {
                statusDiv.innerHTML = `<div class="text-red-600">❌ Error reading data: ${error.message}</div>`;
            }
        }

        function clearAllUsers() {
            if (!confirm('⚠️ This will delete ALL user accounts. Are you sure?')) return;

            try {
                // Remove main users file
                localStorage.removeItem('users');
                
                let deletedCount = 0;
                const keysToDelete = [];

                // Find all user data files
                for (let i = 0; i < localStorage.length; i++) {
                    const key = localStorage.key(i);
                    if (key && key.startsWith('user_')) {
                        keysToDelete.push(key);
                    }
                }

                // Delete user data files
                keysToDelete.forEach(key => {
                    localStorage.removeItem(key);
                    deletedCount++;
                });

                showResult(`✅ Deleted ${deletedCount} user accounts and data files`);
                refreshStatus();

            } catch (error) {
                showResult(`❌ Error: ${error.message}`);
            }
        }

        function clearAllChats() {
            if (!confirm('⚠️ This will delete ALL chat history. Are you sure?')) return;

            try {
                let clearedCount = 0;

                // Clear chats from user data files
                for (let i = 0; i < localStorage.length; i++) {
                    const key = localStorage.key(i);
                    if (key && key.startsWith('user_')) {
                        try {
                            const userData = JSON.parse(localStorage.getItem(key));
                            if (userData.savedChats) {
                                userData.savedChats = [];
                                localStorage.setItem(key, JSON.stringify(userData));
                                clearedCount++;
                            }
                        } catch (e) {
                            console.warn('Could not clear chats for:', key);
                        }
                    }
                }

                // Clear legacy chat storage
                localStorage.removeItem('smokeyChats');
                localStorage.removeItem('smokeyAI_chatHistory');

                showResult(`✅ Cleared chat history for ${clearedCount} users`);
                refreshStatus();

            } catch (error) {
                showResult(`❌ Error: ${error.message}`);
            }
        }

        function clearAllMemory() {
            if (!confirm('⚠️ This will delete ALL memory data. Are you sure?')) return;

            try {
                let clearedCount = 0;

                // Clear memory from user data files
                for (let i = 0; i < localStorage.length; i++) {
                    const key = localStorage.key(i);
                    if (key && key.startsWith('user_')) {
                        try {
                            const userData = JSON.parse(localStorage.getItem(key));
                            if (userData.memory) {
                                userData.memory = {};
                                localStorage.setItem(key, JSON.stringify(userData));
                                clearedCount++;
                            }
                        } catch (e) {
                            console.warn('Could not clear memory for:', key);
                        }
                    }
                }

                // Clear legacy memory storage
                localStorage.removeItem('smokeyMemory');

                showResult(`✅ Cleared memory data for ${clearedCount} users`);
                refreshStatus();

            } catch (error) {
                showResult(`❌ Error: ${error.message}`);
            }
        }

        function clearAllSessions() {
            if (!confirm('⚠️ This will log out all users. Are you sure?')) return;

            try {
                // Clear all session data
                localStorage.removeItem('currentSession');
                localStorage.removeItem('rememberedUser');
                sessionStorage.removeItem('currentSession');

                // Clear any other session-related data
                localStorage.removeItem('currentUser');
                sessionStorage.removeItem('currentUser');

                showResult(`✅ Cleared all login sessions`);
                refreshStatus();

            } catch (error) {
                showResult(`❌ Error: ${error.message}`);
            }
        }

        function clearEverything() {
            if (!confirm('🚨 DANGER: This will delete EVERYTHING and reset Smokey AI to factory state. Are you absolutely sure?')) return;
            if (!confirm('🚨 FINAL WARNING: All users, chats, memory, and settings will be permanently deleted. Continue?')) return;

            try {
                const beforeCount = localStorage.length;
                
                // Nuclear option - clear everything
                localStorage.clear();
                sessionStorage.clear();

                showResult(`🚨 COMPLETE RESET: Deleted ${beforeCount} items. Smokey AI reset to factory state.`);
                refreshStatus();

            } catch (error) {
                showResult(`❌ Error: ${error.message}`);
            }
        }

        function showResult(message) {
            const resultsDiv = document.getElementById('results');
            const resultContent = document.getElementById('resultContent');
            
            resultContent.innerHTML = message;
            resultsDiv.classList.remove('hidden');

            // Auto-hide after 5 seconds
            setTimeout(() => {
                resultsDiv.classList.add('hidden');
            }, 5000);
        }

        // Initialize on page load
        document.addEventListener('DOMContentLoaded', () => {
            refreshStatus();
        });
    </script>
</body>
</html>
