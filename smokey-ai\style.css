/* CSS Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* Dark Theme Colors - Default and Only Theme */
    --primary-color: #8b5cf6;
    --primary-hover: #7c3aed;
    --secondary-color: #1e293b;
    --accent-color: #10b981;
    --background: #0f172a;
    --surface: #1e293b;
    --text-primary: #f1f5f9;
    --text-secondary: #94a3b8;
    --border: #334155;
    --shadow: rgba(0, 0, 0, 0.3);
    --gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);

    /* Additional Dark Theme Colors */
    --surface-hover: #2d3748;
    --surface-active: #4a5568;
    --danger: #ef4444;
    --warning: #f59e0b;
    --success: #10b981;
    --info: #3b82f6;

    /* Spacing */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;

    /* Border Radius */
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    --radius-full: 9999px;

    /* Transitions */
    --transition: all 0.2s ease-in-out;
    --transition-slow: all 0.3s ease-in-out;

    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.3);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.3);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.3);
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background-color: var(--background);
    color: var(--text-primary);
    line-height: 1.6;
    height: 100vh;
    overflow: hidden;
    transition: var(--transition);
}

/* Mobile Menu Toggle */
.mobile-menu-toggle {
    display: none;
    position: fixed;
    top: var(--spacing-md);
    left: var(--spacing-md);
    z-index: 1000;
    background: var(--surface);
    border: 1px solid var(--border);
    border-radius: var(--radius-md);
    padding: var(--spacing-sm);
    cursor: pointer;
    transition: var(--transition);
    flex-direction: column;
    gap: 3px;
    width: 40px;
    height: 40px;
    justify-content: center;
    align-items: center;
}

.mobile-menu-toggle span {
    width: 20px;
    height: 2px;
    background: var(--text-primary);
    transition: var(--transition);
}

.mobile-menu-toggle:hover {
    background: var(--primary-color);
}

.mobile-menu-toggle:hover span {
    background: white;
}

/* Header */
.header {
    background: var(--surface);
    border-bottom: 1px solid var(--border);
    padding: var(--spacing-md) var(--spacing-xl);
    position: sticky;
    top: 0;
    z-index: 100;
    backdrop-filter: blur(10px);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1400px;
    margin: 0 auto;
}

.logo {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.logo-icon {
    font-size: 1.5rem;
    animation: float 3s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-5px); }
}

.logo h1 {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-color);
}

.beta-badge {
    background: var(--accent-color);
    color: white;
    font-size: 0.75rem;
    font-weight: 500;
    padding: 2px 8px;
    border-radius: var(--radius-full);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.header-controls {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.status-indicator {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) var(--spacing-md);
    background: var(--surface);
    border: 1px solid var(--border);
    border-radius: var(--radius-full);
    font-size: 0.875rem;
    color: var(--text-secondary);
}

.status-dot {
    width: 8px;
    height: 8px;
    background: var(--success);
    border-radius: 50%;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

.status-text {
    font-weight: 500;
}

.memory-indicator {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) var(--spacing-md);
    background: var(--surface);
    border: 1px solid var(--border);
    border-radius: var(--radius-full);
    font-size: 0.875rem;
    color: var(--text-secondary);
    cursor: pointer;
    transition: var(--transition);
}

.memory-indicator:hover {
    background: var(--surface-hover);
    transform: translateY(-1px);
}

.memory-icon {
    font-size: 1rem;
    animation: pulse 2s infinite;
}

.memory-count {
    font-weight: 600;
    color: var(--primary-color);
    min-width: 20px;
    text-align: center;
}

.user-indicator {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) var(--spacing-md);
    background: var(--surface);
    border: 1px solid var(--border);
    border-radius: var(--radius-full);
    font-size: 0.875rem;
    color: var(--text-primary);
    transition: var(--transition);
}

.user-indicator:hover {
    background: var(--surface-hover);
    transform: translateY(-1px);
}

.user-avatar {
    font-size: 1rem;
    width: 24px;
    height: 24px;
    background: var(--primary-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 0.75rem;
}

.user-name {
    font-weight: 500;
    color: var(--text-primary);
    max-width: 100px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.profile-btn {
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: var(--spacing-xs);
    border-radius: var(--radius-sm);
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: var(--spacing-xs);
}

.profile-btn:hover {
    color: var(--primary-color);
    background: rgba(139, 92, 246, 0.1);
}

.logout-btn {
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: var(--spacing-xs);
    border-radius: var(--radius-sm);
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
}

.logout-btn:hover {
    color: var(--danger);
    background: rgba(239, 68, 68, 0.1);
}

/* Main Container */
.main-container {
    display: flex;
    height: calc(100vh - 80px);
    overflow: hidden;
}

/* Sidebar */
.sidebar {
    width: 300px;
    background: var(--surface);
    border-right: 1px solid var(--border);
    display: flex;
    flex-direction: column;
    transition: var(--transition-slow);
}

.sidebar-header {
    padding: var(--spacing-xl);
    border-bottom: 1px solid var(--border);
}

.sidebar-header h2 {
    font-size: 1.125rem;
    font-weight: 600;
    margin-bottom: var(--spacing-md);
    color: var(--text-primary);
}

.sidebar-actions {
    display: flex;
    gap: var(--spacing-sm);
}

.new-chat-btn {
    flex: 1;
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: var(--radius-md);
    padding: var(--spacing-md);
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
}

.new-chat-btn:hover {
    background: var(--primary-hover);
    transform: translateY(-1px);
}

.new-chat-btn svg {
    width: 16px;
    height: 16px;
}

.clear-history-btn {
    background: var(--danger);
    color: white;
    border: none;
    border-radius: var(--radius-md);
    padding: var(--spacing-md);
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 44px;
}

.clear-history-btn:hover {
    background: #dc2626;
    transform: translateY(-1px);
}

.clear-history-btn svg {
    width: 16px;
    height: 16px;
}

.sidebar-footer {
    padding: var(--spacing-md);
    border-top: 1px solid var(--border);
    background: var(--surface);
}

.chat-stats {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.875rem;
}

.stat-label {
    color: var(--text-secondary);
}

.stat-value {
    color: var(--primary-color);
    font-weight: 600;
}

.chat-history {
    flex: 1;
    overflow-y: auto;
    padding: var(--spacing-md);
}

.chat-history-item {
    padding: var(--spacing-md);
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: var(--transition);
    margin-bottom: var(--spacing-sm);
    border: 1px solid transparent;
}

.chat-history-item:hover {
    background: var(--secondary-color);
    border-color: var(--border);
}

.chat-history-item.active {
    background: var(--primary-color);
    color: white;
}

/* Chat Container */
.chat-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    position: relative;
    overflow: hidden;
}

/* Welcome Message */
.welcome-message {
    display: flex;
    align-items: center;
    justify-content: center;
    flex: 1;
    padding: var(--spacing-2xl);
    text-align: center;
}

.welcome-content {
    max-width: 500px;
}

.welcome-icon {
    font-size: 4rem;
    margin-bottom: var(--spacing-lg);
    animation: float 3s ease-in-out infinite;
}

.welcome-content h2 {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: var(--spacing-md);
    background: var(--gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.welcome-content p {
    color: var(--text-secondary);
    font-size: 1.125rem;
    margin-bottom: var(--spacing-xl);
    line-height: 1.7;
}

.welcome-features {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: var(--spacing-lg);
}

.feature {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md);
    background: var(--surface);
    border: 1px solid var(--border);
    border-radius: var(--radius-lg);
    transition: var(--transition);
}

.feature:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px var(--shadow);
}

.feature-icon {
    font-size: 1.25rem;
}

/* Chat Output */
.chat-output {
    flex: 1;
    overflow-y: auto;
    padding: var(--spacing-xl);
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.chat-output::-webkit-scrollbar {
    width: 6px;
}

.chat-output::-webkit-scrollbar-track {
    background: transparent;
}

.chat-output::-webkit-scrollbar-thumb {
    background: var(--border);
    border-radius: var(--radius-full);
}

.chat-output::-webkit-scrollbar-thumb:hover {
    background: var(--text-secondary);
}

/* Messages */
.message {
    max-width: 80%;
    padding: var(--spacing-md) var(--spacing-lg);
    border-radius: var(--radius-xl);
    word-wrap: break-word;
    line-height: 1.6;
    animation: slideIn 0.3s ease-out;
    position: relative;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.message.user {
    background: var(--primary-color);
    color: white;
    align-self: flex-end;
    border-bottom-right-radius: var(--radius-sm);
    margin-left: auto;
}

.message.ai {
    background: var(--surface);
    color: var(--text-primary);
    border: 1px solid var(--border);
    align-self: flex-start;
    border-bottom-left-radius: var(--radius-sm);
}

.message.error {
    background: #ef4444;
    color: white;
    align-self: center;
    text-align: center;
}

/* ChatGPT-Style Message Formatting */
.message.ai {
    line-height: 1.7;
}

.formatted-paragraph {
    margin-bottom: var(--spacing-md);
    line-height: 1.6;
}

.formatted-paragraph:last-child {
    margin-bottom: 0;
}

.formatted-list {
    margin: var(--spacing-md) 0;
    padding-left: var(--spacing-xl);
}

.formatted-list li {
    margin-bottom: var(--spacing-sm);
    line-height: 1.6;
}

.formatted-list li:last-child {
    margin-bottom: 0;
}

/* Code Formatting */
.code-block {
    background: #1a1a1a;
    border: 1px solid var(--border);
    border-radius: var(--radius-lg);
    margin: var(--spacing-md) 0;
    overflow: hidden;
    font-family: 'Fira Code', 'Monaco', 'Cascadia Code', 'Roboto Mono', monospace;
}

.code-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-sm) var(--spacing-md);
    background: #2d2d2d;
    border-bottom: 1px solid var(--border);
}

.code-language {
    font-size: 0.75rem;
    color: var(--text-secondary);
    text-transform: uppercase;
    font-weight: 600;
    letter-spacing: 0.5px;
}

.copy-code-btn {
    background: transparent;
    border: 1px solid var(--border);
    border-radius: var(--radius-sm);
    padding: 4px 8px;
    color: var(--text-secondary);
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.75rem;
}

.copy-code-btn:hover {
    background: var(--surface-hover);
    color: var(--text-primary);
}

.copy-code-btn svg {
    width: 14px;
    height: 14px;
}

.code-block pre {
    margin: 0;
    padding: var(--spacing-md);
    overflow-x: auto;
    background: #1a1a1a;
    color: #e6e6e6;
    font-size: 0.875rem;
    line-height: 1.5;
}

.code-block code {
    font-family: inherit;
    background: none;
    padding: 0;
    border-radius: 0;
    color: inherit;
}

.inline-code {
    background: var(--surface-hover);
    color: var(--primary-color);
    padding: 2px 6px;
    border-radius: var(--radius-sm);
    font-family: 'Fira Code', 'Monaco', 'Cascadia Code', 'Roboto Mono', monospace;
    font-size: 0.875em;
    font-weight: 500;
    border: 1px solid var(--border);
}

/* Text Formatting */
.message.ai strong {
    font-weight: 600;
    color: var(--text-primary);
}

.message.ai em {
    font-style: italic;
    color: var(--text-secondary);
}

/* Syntax Highlighting for Common Languages */
.language-javascript .token.keyword,
.language-js .token.keyword {
    color: #ff79c6;
}

.language-javascript .token.string,
.language-js .token.string {
    color: #f1fa8c;
}

.language-javascript .token.function,
.language-js .token.function {
    color: #50fa7b;
}

.language-javascript .token.number,
.language-js .token.number {
    color: #bd93f9;
}

.language-python .token.keyword {
    color: #ff79c6;
}

.language-python .token.string {
    color: #f1fa8c;
}

.language-python .token.function {
    color: #50fa7b;
}

.language-html .token.tag {
    color: #ff79c6;
}

.language-html .token.attr-name {
    color: #50fa7b;
}

.language-html .token.attr-value {
    color: #f1fa8c;
}

.language-css .token.property {
    color: #50fa7b;
}

.language-css .token.string {
    color: #f1fa8c;
}

.language-css .token.selector {
    color: #ff79c6;
}

/* Loading Animation */
.loading-message {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md) var(--spacing-lg);
    background: var(--surface);
    border: 1px solid var(--border);
    border-radius: var(--radius-xl);
    border-bottom-left-radius: var(--radius-sm);
    max-width: 80%;
    align-self: flex-start;
}

.loading-dots {
    display: flex;
    gap: 4px;
}

.loading-dot {
    width: 8px;
    height: 8px;
    background: var(--primary-color);
    border-radius: 50%;
    animation: bounce 1.4s ease-in-out infinite both;
}

.loading-dot:nth-child(1) { animation-delay: -0.32s; }
.loading-dot:nth-child(2) { animation-delay: -0.16s; }
.loading-dot:nth-child(3) { animation-delay: 0s; }

@keyframes bounce {
    0%, 80%, 100% {
        transform: scale(0);
    }
    40% {
        transform: scale(1);
    }
}

/* Quick Actions */
.quick-actions {
    display: flex;
    justify-content: space-evenly;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-lg) var(--spacing-xl);
    background: var(--surface);
    border-top: 1px solid var(--border);
    overflow-x: auto;
    min-height: 70px;
}

.quick-action-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md) var(--spacing-lg);
    background: var(--background);
    border: 1px solid var(--border);
    border-radius: var(--radius-full);
    color: var(--text-primary);
    cursor: pointer;
    transition: var(--transition);
    white-space: nowrap;
    font-size: 0.875rem;
    font-weight: 500;
    min-width: 120px;
    height: 44px;
    flex: 1;
    max-width: 160px;
    text-align: center;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.quick-action-btn:hover {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(139, 92, 246, 0.3);
}

.quick-action-btn:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.action-icon {
    font-size: 1.1rem;
    flex-shrink: 0;
}

.action-text {
    font-weight: 500;
    flex-shrink: 0;
}

/* Input Area */
.input-area {
    background: var(--surface);
    border-top: 1px solid var(--border);
    padding: var(--spacing-xl);
}

.input-container {
    display: flex;
    gap: var(--spacing-md);
    align-items: flex-end;
    max-width: 800px;
    margin: 0 auto;
    position: relative;
}

.input-container textarea {
    flex: 1;
    background: var(--background);
    border: 1px solid var(--border);
    border-radius: var(--radius-xl);
    padding: var(--spacing-md) var(--spacing-lg);
    font-size: 1rem;
    color: var(--text-primary);
    transition: var(--transition);
    resize: none;
    min-height: 44px;
    max-height: 120px;
    font-family: inherit;
    line-height: 1.5;
}

.input-container textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(139, 92, 246, 0.1);
}

.input-container textarea::placeholder {
    color: var(--text-secondary);
}

.input-actions {
    display: flex;
    gap: var(--spacing-sm);
    align-items: flex-end;
}

.action-button {
    background: var(--surface-hover);
    color: var(--text-secondary);
    border: 1px solid var(--border);
    border-radius: var(--radius-md);
    padding: var(--spacing-sm);
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
}

.action-button:hover {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.action-button svg {
    width: 18px;
    height: 18px;
}

.send-button {
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: var(--radius-xl);
    padding: var(--spacing-md);
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 44px;
    height: 44px;
}

.send-button:hover:not(:disabled) {
    background: var(--primary-hover);
    transform: translateY(-1px);
}

.send-button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.send-button svg {
    width: 20px;
    height: 20px;
}

.input-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: var(--spacing-sm);
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
}

.input-info {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.typing-indicator {
    font-size: 0.875rem;
    color: var(--primary-color);
    font-weight: 500;
    display: none;
    animation: pulse 1.5s infinite;
}

.typing-indicator.show {
    display: block;
}

.character-count {
    font-size: 0.875rem;
    color: var(--text-secondary);
}

.input-shortcuts {
    display: flex;
    gap: var(--spacing-md);
}

.shortcut {
    font-size: 0.75rem;
    color: var(--text-secondary);
    background: var(--surface-hover);
    padding: 2px 6px;
    border-radius: var(--radius-sm);
    font-family: monospace;
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 9999;
}

.loading-overlay.show {
    display: flex;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-top: 4px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
    .mobile-menu-toggle {
        display: flex;
    }

    .header {
        padding: var(--spacing-md);
    }

    .header-content {
        padding: 0 var(--spacing-xl);
    }

    .main-container {
        height: calc(100vh - 70px);
    }

    .sidebar {
        position: fixed;
        top: 70px;
        left: 0;
        height: calc(100vh - 70px);
        z-index: 999;
        transform: translateX(-100%);
        transition: transform 0.3s ease-in-out;
    }

    .sidebar.open {
        transform: translateX(0);
    }

    .chat-container {
        width: 100%;
    }

    .welcome-message {
        padding: var(--spacing-xl);
    }

    .welcome-content h2 {
        font-size: 1.5rem;
    }

    .welcome-features {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }

    .chat-output {
        padding: var(--spacing-md);
    }

    .message {
        max-width: 90%;
    }

    /* Quick Actions Mobile Optimization */
    .quick-actions {
        padding: var(--spacing-md);
        gap: var(--spacing-sm);
        overflow-x: auto;
        scrollbar-width: none;
        -ms-overflow-style: none;
    }

    .quick-actions::-webkit-scrollbar {
        display: none;
    }

    .quick-action-btn {
        min-width: 100px;
        max-width: none;
        flex: 0 0 auto;
        padding: var(--spacing-sm) var(--spacing-md);
        font-size: 0.8rem;
    }

    .action-text {
        display: none;
    }

    .action-icon {
        font-size: 1.2rem;
    }

    .input-area {
        padding: var(--spacing-md);
    }

    .input-container {
        gap: var(--spacing-sm);
    }
}

@media (max-width: 480px) {
    .header-content {
        padding: 0 var(--spacing-md);
    }

    .logo h1 {
        font-size: 1.25rem;
    }

    .welcome-icon {
        font-size: 3rem;
    }

    .welcome-content h2 {
        font-size: 1.25rem;
    }

    .welcome-content p {
        font-size: 1rem;
    }

    .message {
        max-width: 95%;
        padding: var(--spacing-sm) var(--spacing-md);
    }

    .input-container textarea {
        font-size: 16px; /* Prevents zoom on iOS */
    }

    /* Ultra-compact quick actions for small screens */
    .quick-actions {
        padding: var(--spacing-sm);
        min-height: 60px;
    }

    .quick-action-btn {
        min-width: 80px;
        height: 40px;
        padding: var(--spacing-xs) var(--spacing-sm);
    }

    .action-icon {
        font-size: 1.4rem;
    }
}

/* Utility Classes */
.hidden {
    display: none !important;
}

.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* Smooth scrolling */
html {
    scroll-behavior: smooth;
}

/* Focus styles for accessibility */
*:focus-visible {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* Image Generation Modal */
.image-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 1001;
    backdrop-filter: blur(8px);
    padding: var(--spacing-lg);
}

.image-modal.active {
    display: flex;
}

.image-modal-content {
    background: var(--background);
    border-radius: var(--radius-xl);
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    width: 100%;
    max-width: 600px;
    max-height: 90vh;
    overflow-y: auto;
    border: 1px solid var(--border);
    animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-20px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.image-modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-xl);
    border-bottom: 1px solid var(--border);
}

.image-modal-header h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.close-modal-btn {
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: var(--spacing-sm);
    border-radius: var(--radius-md);
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
}

.close-modal-btn:hover {
    background: var(--surface-hover);
    color: var(--text-primary);
}

.close-modal-btn svg {
    width: 20px;
    height: 20px;
}

.image-modal-body {
    padding: var(--spacing-xl);
}

.image-prompt-section {
    margin-bottom: var(--spacing-xl);
}

.image-prompt-section label {
    display: block;
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
}

#image-prompt {
    width: 100%;
    padding: var(--spacing-md);
    border: 1px solid var(--border);
    border-radius: var(--radius-md);
    background: var(--surface);
    color: var(--text-primary);
    font-family: inherit;
    font-size: 0.875rem;
    line-height: 1.5;
    resize: vertical;
    transition: var(--transition);
}

#image-prompt:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(139, 92, 246, 0.1);
}

.prompt-counter {
    text-align: right;
    margin-top: var(--spacing-xs);
}

#prompt-counter {
    font-size: 0.75rem;
    color: var(--text-secondary);
}

.image-settings {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
}

.setting-group label {
    display: block;
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
    font-size: 0.875rem;
}

.setting-group select {
    width: 100%;
    padding: var(--spacing-sm) var(--spacing-md);
    border: 1px solid var(--border);
    border-radius: var(--radius-md);
    background: var(--surface);
    color: var(--text-primary);
    font-family: inherit;
    font-size: 0.875rem;
    cursor: pointer;
    transition: var(--transition);
}

.setting-group select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(139, 92, 246, 0.1);
}

.image-examples {
    margin-bottom: var(--spacing-lg);
}

.image-examples h4 {
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
}

.example-prompts {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-sm);
}

.example-prompt {
    background: var(--surface);
    border: 1px solid var(--border);
    border-radius: var(--radius-md);
    padding: var(--spacing-sm) var(--spacing-md);
    color: var(--text-secondary);
    font-size: 0.75rem;
    cursor: pointer;
    transition: var(--transition);
    text-align: left;
}

.example-prompt:hover {
    background: var(--surface-hover);
    border-color: var(--primary-color);
    color: var(--text-primary);
}

.image-modal-footer {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    gap: var(--spacing-md);
    padding: var(--spacing-xl);
    border-top: 1px solid var(--border);
}

.cancel-btn {
    background: none;
    border: 1px solid var(--border);
    color: var(--text-secondary);
    padding: var(--spacing-sm) var(--spacing-lg);
    border-radius: var(--radius-md);
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
}

.cancel-btn:hover {
    background: var(--surface-hover);
    color: var(--text-primary);
}

.generate-btn {
    background: var(--primary-color);
    border: none;
    color: white;
    padding: var(--spacing-sm) var(--spacing-xl);
    border-radius: var(--radius-md);
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    min-width: 140px;
    justify-content: center;
}

.generate-btn:hover {
    background: var(--primary-hover);
}

.generate-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.btn-spinner {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.btn-spinner .spinner {
    width: 16px;
    height: 16px;
    animation: spin 1s linear infinite;
}

/* Generated Image Display */
.generated-image {
    max-width: 100%;
    border-radius: var(--radius-lg);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    margin: var(--spacing-md) 0;
}

.image-message {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.image-info {
    font-size: 0.875rem;
    color: var(--text-secondary);
    padding: var(--spacing-sm);
    background: var(--surface);
    border-radius: var(--radius-md);
    border: 1px solid var(--border);
}

.image-actions {
    display: flex;
    gap: var(--spacing-sm);
    flex-wrap: wrap;
}

.image-action-btn {
    background: var(--surface);
    border: 1px solid var(--border);
    color: var(--text-secondary);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-sm);
    font-size: 0.75rem;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.image-action-btn:hover {
    background: var(--surface-hover);
    color: var(--text-primary);
    border-color: var(--primary-color);
}

.image-action-btn svg {
    width: 12px;
    height: 12px;
}
