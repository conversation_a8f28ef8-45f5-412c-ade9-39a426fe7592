<!-- index.html -->
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>NutriTrack - Indian Calorie Tracker</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://unpkg.com/lucide@latest"></script>
</head>
<body data-theme="dark">
    <nav class="main-nav">
        <div class="nav-brand">
            <span class="logo-icon">🍛</span>
            <h1>NutriTrack</h1>
        </div>
        <div class="nav-controls">
            <div class="daily-goal">
                <span id="dailyCalories">0</span>/<span id="targetCalories">2000</span> kcal
            </div>
            <button id="themeToggle" class="icon-btn" aria-label="Toggle theme">
                <i data-lucide="moon"></i>
            </button>
        </div>
    </nav>

    <main class="app-container">
        <section class="calorie-summary">
            <div class="glass-card">
                <canvas id="calorieChart"></canvas>
                <div class="chart-center">
                    <span id="caloriePercentage">0%</span>
                    <small>of daily goal</small>
                </div>
            </div>
        </section>

        <section class="nutrient-breakdown">
            <h2 class="section-title">Nutrient Breakdown</h2>
            <div class="glass-card">
                <canvas id="nutrientChart"></canvas>
            </div>
        </section>

        <section class="meal-log">
            <div class="section-header">
                <h2 class="section-title">Today's Meals</h2>
                <button id="addMealBtn" class="primary-btn">
                    <i data-lucide="plus"></i> Add Meal
                </button>
            </div>
            <div id="mealList" class="meal-list">
                <!-- Meals will be inserted here -->
            </div>
        </section>
    </main>

    <div class="mobile-nav">
        <button class="mobile-nav-btn active">
            <i data-lucide="home"></i>
            <span>Home</span>
        </button>
        <button class="mobile-nav-btn">
            <i data-lucide="history"></i>
            <span>History</span>
        </button>
        <button class="mobile-nav-btn">
            <i data-lucide="settings"></i>
            <span>Settings</span>
        </button>
    </div>

    <div id="addMealModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Add New Meal</h3>
                <button id="closeModal" class="icon-btn">
                    <i data-lucide="x"></i>
                </button>
            </div>
            <form id="mealForm" class="meal-form">
                <div class="form-group autocomplete">
                    <label for="mealName">Meal Name</label>
                    <input type="text" id="mealName" placeholder="Search Indian dishes..." autocomplete="off">
                    <div class="autocomplete-results"></div>
                </div>
                <div class="form-group">
                    <label for="portionSize">Portion Size</label>
                    <select id="portionSize">
                        <option value="0.5">Half portion</option>
                        <option value="1" selected>1 portion</option>
                        <option value="1.5">1.5 portions</option>
                        <option value="2">2 portions</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="mealCalories">Calories</label>
                    <input type="number" id="mealCalories" placeholder="Calories">
                </div>
                <button type="submit" class="primary-btn submit-btn">
                    <i data-lucide="check"></i> Add Meal
                </button>
            </form>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>