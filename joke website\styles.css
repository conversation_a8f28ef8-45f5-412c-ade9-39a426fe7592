:root {
    --bg-primary: #1a1a2e;
    --bg-secondary: #16213e;
    --text-primary: #ffffff;
    --text-secondary: #e6e6e6;
    --accent: #ff6b6b;
    --accent-hover: #ff8e8e;
    --gradient-start: #ff9a9e;
    --gradient-mid: #fad0c4;
    --gradient-end: #fbc2eb;
    --joke-bg: rgba(255, 255, 255, 0.1);
    --shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s ease;
}

[data-theme="light"] {
    --bg-primary: #f8f9fa;
    --bg-secondary: #e9ecef;
    --text-primary: #212529;
    --text-secondary: #495057;
    --joke-bg: rgba(0, 0, 0, 0.05);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Baloo 2', cursive;
    background-color: var(--bg-primary);
    color: var(--text-primary);
    min-height: 100vh;
    transition: var(--transition);
    line-height: 1.6;
}

.container {
    max-width: 800px;
    margin: 0 auto;
    padding: 2rem;
    text-align: center;
}

#theme-toggle {
    position: absolute;
    top: 1rem;
    right: 1rem;
    background: none;
    border: none;
    color: var(--text-primary);
    font-size: 1.5rem;
    cursor: pointer;
    transition: var(--transition);
}

#theme-toggle:hover {
    transform: rotate(30deg);
}

.title {
    font-size: 3.5rem;
    margin-bottom: 0.5rem;
    background: linear-gradient(45deg, var(--gradient-start), var(--gradient-mid), var(--gradient-end));
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.2);
}

.subtitle {
    font-size: 1.2rem;
    color: var(--text-secondary);
    margin-bottom: 2rem;
}

.joke-container {
    margin: 2rem 0;
    min-height: 150px;
}

.joke-display {
    background-color: var(--joke-bg);
    padding: 1.5rem;
    border-radius: 15px;
    margin-bottom: 1rem;
    box-shadow: var(--shadow);
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.joke-display::after {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(
        to bottom right,
        rgba(255, 255, 255, 0) 0%,
        rgba(255, 255, 255, 0) 50%,
        rgba(255, 255, 255, 0.1) 50%,
        rgba(255, 255, 255, 0.1) 100%
    );
    transform: rotate(30deg);
    animation: shine 3s infinite;
}

@keyframes shine {
    0% {
        transform: translateX(-100%) rotate(30deg);
    }
    100% {
        transform: translateX(100%) rotate(30deg);
    }
}

.joke-display p {
    font-size: 1.3rem;
    margin-bottom: 0.5rem;
}

.placeholder {
    color: var(--text-secondary);
    font-style: italic;
}

.emoji-reaction {
    font-size: 2rem;
    min-height: 2.5rem;
    margin-top: 1rem;
    transition: var(--transition);
}

.joke-button {
    background-color: var(--accent);
    color: white;
    border: none;
    padding: 1rem 2rem;
    font-size: 1.2rem;
    border-radius: 50px;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    margin: 0 auto;
    box-shadow: 0 4px 15px rgba(255, 107, 107, 0.4);
    position: relative;
    overflow: hidden;
}

.joke-button:hover {
    background-color: var(--accent-hover);
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(255, 107, 107, 0.6);
}

.joke-button:active {
    transform: translateY(1px);
}

.joke-button .button-icon {
    transition: var(--transition);
}

.joke-button:hover .button-icon {
    animation: bounce 0.5s infinite alternate;
}

@keyframes bounce {
    from {
        transform: translateY(0);
    }
    to {
        transform: translateY(-5px);
    }
}

.save-section {
    margin-top: 2rem;
}

.save-button {
    background-color: transparent;
    color: var(--accent);
    border: 2px solid var(--accent);
    padding: 0.5rem 1rem;
    border-radius: 50px;
    cursor: pointer;
    transition: var(--transition);
    margin-bottom: 1rem;
}

.save-button:hover {
    background-color: var(--accent);
    color: white;
}

.saved-jokes {
    background-color: var(--bg-secondary);
    padding: 1rem;
    border-radius: 15px;
    margin-top: 1rem;
    text-align: left;
}

.saved-jokes h3 {
    margin-bottom: 0.5rem;
    color: var(--text-primary);
}

.saved-jokes ul {
    list-style-type: none;
}

.saved-jokes li {
    padding: 0.5rem 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.saved-jokes li:last-child {
    border-bottom: none;
}

/* Animations */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in {
    animation: fadeIn 0.5s ease-out;
}

@keyframes emojiPop {
    0% {
        transform: scale(0);
    }
    50% {
        transform: scale(1.2);
    }
    100% {
        transform: scale(1);
    }
}

.emoji-pop {
    animation: emojiPop 0.5s ease-out;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 1rem;
    }
    
    .title {
        font-size: 2.5rem;
    }
    
    .joke-display p {
        font-size: 1.1rem;
    }
    
    .joke-button {
        padding: 0.8rem 1.5rem;
        font-size: 1rem;
    }
}