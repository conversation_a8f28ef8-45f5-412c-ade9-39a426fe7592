<!DOCTYPE html>
<html lang="en" data-theme="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WeatherSphere - Multi-City Weather Dashboard</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Fredoka+One&family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/weather-icons/2.0.12/css/weather-icons.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">
</head>
<body>
    <div class="app-container">
        <!-- Header Section -->
        <header class="app-header animate__animated animate__fadeInDown">
            <div class="header-content">
                <h1 class="app-title">
                    <i class="wi wi-day-cloudy-high"></i> WeatherSphere
                </h1>
                <p class="app-subtitle">Your global weather dashboard</p>
            </div>
            <div class="header-controls">
                <button id="theme-toggle" class="control-btn" aria-label="Toggle theme" title="Toggle dark/light mode">
                    <i class="fas fa-moon"></i>
                </button>
                <div class="unit-toggle">
                    <button id="celsius-btn" class="unit-btn active" data-unit="metric" title="Celsius">°C</button>
                    <button id="fahrenheit-btn" class="unit-btn" data-unit="imperial" title="Fahrenheit">°F</button>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Search Section -->
            <section class="search-section animate__animated animate__fadeIn">
                <div class="search-container">
                    <div class="search-input-container">
                        <i class="fas fa-search search-icon"></i>
                        <input type="text" id="city-input" class="city-input" 
                               placeholder="Enter city name..." 
                               autocomplete="off"
                               aria-label="City search input">
                        <button id="add-city-btn" class="add-city-btn" aria-label="Add city">
                            <i class="fas fa-plus"></i> Add City
                        </button>
                    </div>
                    <div class="search-hint">
                        <i class="fas fa-info-circle"></i> Try "Paris, London, Tokyo"
                    </div>
                </div>
            </section>

            <!-- Current Weather Cards -->
            <section class="weather-cards-section">
                <div class="section-header">
                    <h2><i class="fas fa-map-marker-alt"></i> Your Locations</h2>
                    <button id="refresh-all-btn" class="refresh-btn" title="Refresh all cities">
                        <i class="fas fa-sync-alt"></i> Refresh All
                    </button>
                </div>
                
                <div class="weather-cards" id="weather-cards">
                    <!-- Default welcome card -->
                    <div class="welcome-card animate__animated animate__fadeIn">
                        <div class="welcome-icon">
                            <i class="wi wi-day-sunny"></i>
                        </div>
                        <h3>Welcome to WeatherSphere!</h3>
                        <p>Add cities to see their current weather conditions and forecasts</p>
                        <div class="sample-cities">
                            <button class="sample-city" data-city="New York">New York</button>
                            <button class="sample-city" data-city="London">London</button>
                            <button class="sample-city" data-city="Tokyo">Tokyo</button>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Forecast Section -->
            <section class="forecast-section" id="forecast-section">
                <div class="section-header">
                    <h2><i class="fas fa-calendar-alt"></i> 3-Day Forecast</h2>
                    <div class="forecast-city" id="forecast-city-name">Select a city to view forecast</div>
                </div>
                <div class="forecast-cards" id="forecast-cards">
                    <!-- Forecast cards will be inserted here by JavaScript -->
                </div>
            </section>
        </main>

        <!-- Footer -->
        <footer class="app-footer">
            <p>Powered by OpenWeatherMap API</p>
            <p class="last-updated">Last updated: <span id="last-updated-time">Never</span></p>
        </footer>
    </div>

    <!-- Loading Overlay -->
    <div class="loading-overlay" id="loading-overlay">
        <div class="loading-content">
            <div class="weather-spinner">
                <i class="wi wi-day-cloudy"></i>
                <i class="wi wi-day-sunny"></i>
                <i class="wi wi-rain"></i>
            </div>
            <p>Fetching weather data...</p>
        </div>
    </div>

    <!-- Weather Animation Elements -->
    <div class="weather-animation" id="weather-animation">
        <!-- Animation elements will be added by JavaScript based on weather -->
    </div>

    <script src="script.js"></script>
</body>
</html>