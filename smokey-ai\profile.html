<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>User Profile - Smokey AI v1.5</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'inter': ['Inter', 'sans-serif'],
                    },
                    colors: {
                        'primary': '#8b5cf6',
                        'primary-dark': '#7c3aed',
                        'dark-bg': '#0f172a',
                        'dark-surface': '#1e293b',
                        'dark-border': '#334155',
                    }
                }
            }
        }
    </script>
    <style>
        body {
            font-family: 'Inter', sans-serif;
        }
        
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        .glass-effect {
            backdrop-filter: blur(20px);
            background: rgba(30, 41, 59, 0.8);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .input-glow:focus {
            box-shadow: 0 0 0 3px rgba(139, 92, 246, 0.3);
        }
        
        .floating-animation {
            animation: float 6s ease-in-out infinite;
        }
        
        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
        }

        .avatar-upload {
            position: relative;
            overflow: hidden;
            cursor: pointer;
        }

        .avatar-upload input[type=file] {
            position: absolute;
            left: -9999px;
        }

        .avatar-preview {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            object-fit: cover;
            border: 4px solid rgba(139, 92, 246, 0.3);
            transition: all 0.3s ease;
        }

        .avatar-upload:hover .avatar-preview {
            border-color: rgba(139, 92, 246, 0.6);
            transform: scale(1.05);
        }

        .avatar-placeholder {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            background: linear-gradient(135deg, #8b5cf6, #7c3aed);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 3rem;
            color: white;
            border: 4px solid rgba(139, 92, 246, 0.3);
            transition: all 0.3s ease;
        }

        .avatar-upload:hover .avatar-placeholder {
            border-color: rgba(139, 92, 246, 0.6);
            transform: scale(1.05);
        }
    </style>
</head>
<body class="min-h-screen gradient-bg">
    <!-- Background Elements -->
    <div class="absolute inset-0 overflow-hidden">
        <div class="absolute top-1/4 left-1/4 w-64 h-64 bg-purple-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 floating-animation"></div>
        <div class="absolute top-3/4 right-1/4 w-64 h-64 bg-blue-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 floating-animation" style="animation-delay: 2s;"></div>
        <div class="absolute bottom-1/4 left-1/3 w-64 h-64 bg-pink-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 floating-animation" style="animation-delay: 4s;"></div>
    </div>

    <!-- Navigation -->
    <nav class="relative z-10 p-6">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
                <a href="index.html" class="flex items-center space-x-2 text-white hover:text-purple-200 transition-colors">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                    </svg>
                    <span class="font-medium">Back to Chat</span>
                </a>
            </div>
            <div class="flex items-center space-x-2 text-white">
                <span class="text-3xl">🐾</span>
                <h1 class="text-xl font-bold">Smokey AI v1.5</h1>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="relative z-10 max-w-4xl mx-auto px-6 pb-12">
        <!-- Header -->
        <div class="text-center mb-8">
            <h1 class="text-4xl font-bold text-white mb-2">👤 User Profile</h1>
            <p class="text-gray-300">Manage your account settings and preferences</p>
        </div>

        <!-- Profile Container -->
        <div class="glass-effect rounded-2xl p-8 shadow-2xl">
            <!-- Profile Header -->
            <div class="flex flex-col md:flex-row items-center md:items-start space-y-6 md:space-y-0 md:space-x-8 mb-8">
                <!-- Avatar Section -->
                <div class="flex flex-col items-center">
                    <div class="avatar-upload" onclick="document.getElementById('avatarInput').click()">
                        <div id="avatarPreview" class="avatar-placeholder">
                            👤
                        </div>
                        <input type="file" id="avatarInput" accept="image/*" onchange="handleAvatarUpload(event)">
                    </div>
                    <button onclick="document.getElementById('avatarInput').click()" class="mt-3 text-sm text-purple-300 hover:text-purple-100 transition-colors">
                        📷 Change Avatar
                    </button>
                    <button onclick="removeAvatar()" class="mt-1 text-xs text-gray-400 hover:text-gray-200 transition-colors">
                        🗑️ Remove
                    </button>
                </div>

                <!-- User Info -->
                <div class="flex-1 text-center md:text-left">
                    <h2 id="displayUsername" class="text-2xl font-bold text-white mb-2">Loading...</h2>
                    <p id="displayEmail" class="text-gray-300 mb-4">Loading...</p>
                    <div class="flex flex-wrap gap-2 justify-center md:justify-start">
                        <span class="px-3 py-1 bg-purple-500 bg-opacity-30 text-purple-200 rounded-full text-sm">
                            🎨 Image Generator
                        </span>
                        <span class="px-3 py-1 bg-blue-500 bg-opacity-30 text-blue-200 rounded-full text-sm">
                            🧠 Memory System
                        </span>
                        <span class="px-3 py-1 bg-green-500 bg-opacity-30 text-green-200 rounded-full text-sm">
                            💬 Multi-Chat
                        </span>
                    </div>
                </div>

                <!-- Stats -->
                <div class="grid grid-cols-3 gap-4 text-center">
                    <div class="glass-effect rounded-lg p-3">
                        <div id="chatCount" class="text-xl font-bold text-white">0</div>
                        <div class="text-xs text-gray-300">Chats</div>
                    </div>
                    <div class="glass-effect rounded-lg p-3">
                        <div id="memoryCount" class="text-xl font-bold text-white">0</div>
                        <div class="text-xs text-gray-300">Memories</div>
                    </div>
                    <div class="glass-effect rounded-lg p-3">
                        <div id="daysSince" class="text-xl font-bold text-white">0</div>
                        <div class="text-xs text-gray-300">Days</div>
                    </div>
                </div>
            </div>

            <!-- Settings Form -->
            <form id="profileForm" class="space-y-6">
                <!-- Account Settings -->
                <div class="glass-effect rounded-xl p-6">
                    <h3 class="text-xl font-semibold text-white mb-4 flex items-center">
                        <span class="mr-2">⚙️</span>
                        Account Settings
                    </h3>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Username -->
                        <div>
                            <label for="username" class="block text-sm font-medium text-gray-300 mb-2">
                                Username
                            </label>
                            <input 
                                type="text" 
                                id="username" 
                                name="username"
                                placeholder="Enter username"
                                class="w-full px-4 py-3 bg-dark-surface border border-dark-border rounded-xl text-white placeholder-gray-400 focus:outline-none focus:border-primary input-glow transition-all duration-200"
                                required
                                minlength="3"
                                maxlength="20"
                                pattern="^[a-zA-Z0-9_]+$"
                            >
                            <div id="usernameError" class="text-red-400 text-sm mt-1 hidden"></div>
                            <div class="text-xs text-gray-400 mt-1">3-20 characters, letters, numbers, and underscores only</div>
                        </div>

                        <!-- Email -->
                        <div>
                            <label for="email" class="block text-sm font-medium text-gray-300 mb-2">
                                Email Address
                            </label>
                            <input 
                                type="email" 
                                id="email" 
                                name="email"
                                placeholder="Enter email address"
                                class="w-full px-4 py-3 bg-dark-surface border border-dark-border rounded-xl text-white placeholder-gray-400 focus:outline-none focus:border-primary input-glow transition-all duration-200"
                                required
                            >
                            <div id="emailError" class="text-red-400 text-sm mt-1 hidden"></div>
                        </div>
                    </div>
                </div>

                <!-- Security Settings -->
                <div class="glass-effect rounded-xl p-6">
                    <h3 class="text-xl font-semibold text-white mb-4 flex items-center">
                        <span class="mr-2">🔒</span>
                        Security Settings
                    </h3>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Current Password -->
                        <div>
                            <label for="currentPassword" class="block text-sm font-medium text-gray-300 mb-2">
                                Current Password
                            </label>
                            <div class="relative">
                                <input 
                                    type="password" 
                                    id="currentPassword" 
                                    name="currentPassword"
                                    placeholder="Enter current password"
                                    class="w-full px-4 py-3 bg-dark-surface border border-dark-border rounded-xl text-white placeholder-gray-400 focus:outline-none focus:border-primary input-glow transition-all duration-200"
                                >
                                <button 
                                    type="button" 
                                    onclick="togglePassword('currentPassword')"
                                    class="absolute inset-y-0 right-0 flex items-center pr-3 text-gray-400 hover:text-white transition-colors"
                                >
                                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                    </svg>
                                </button>
                            </div>
                            <div id="currentPasswordError" class="text-red-400 text-sm mt-1 hidden"></div>
                            <div class="text-xs text-gray-400 mt-1">Required to change password</div>
                        </div>

                        <!-- New Password -->
                        <div>
                            <label for="newPassword" class="block text-sm font-medium text-gray-300 mb-2">
                                New Password
                            </label>
                            <div class="relative">
                                <input 
                                    type="password" 
                                    id="newPassword" 
                                    name="newPassword"
                                    placeholder="Enter new password (optional)"
                                    class="w-full px-4 py-3 bg-dark-surface border border-dark-border rounded-xl text-white placeholder-gray-400 focus:outline-none focus:border-primary input-glow transition-all duration-200"
                                    minlength="6"
                                >
                                <button 
                                    type="button" 
                                    onclick="togglePassword('newPassword')"
                                    class="absolute inset-y-0 right-0 flex items-center pr-3 text-gray-400 hover:text-white transition-colors"
                                >
                                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                    </svg>
                                </button>
                            </div>
                            <div id="newPasswordError" class="text-red-400 text-sm mt-1 hidden"></div>
                            <div class="text-xs text-gray-400 mt-1">Leave blank to keep current password</div>
                        </div>
                    </div>
                </div>

                <!-- Preferences -->
                <div class="glass-effect rounded-xl p-6">
                    <h3 class="text-xl font-semibold text-white mb-4 flex items-center">
                        <span class="mr-2">🎨</span>
                        Preferences
                    </h3>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Theme -->
                        <div>
                            <label for="theme" class="block text-sm font-medium text-gray-300 mb-2">
                                Theme
                            </label>
                            <select 
                                id="theme" 
                                name="theme"
                                class="w-full px-4 py-3 bg-dark-surface border border-dark-border rounded-xl text-white focus:outline-none focus:border-primary input-glow transition-all duration-200"
                            >
                                <option value="dark">🌙 Dark Mode</option>
                                <option value="light">☀️ Light Mode</option>
                                <option value="auto">🔄 Auto (System)</option>
                            </select>
                        </div>

                        <!-- Notifications -->
                        <div>
                            <label for="notifications" class="block text-sm font-medium text-gray-300 mb-2">
                                Notifications
                            </label>
                            <select 
                                id="notifications" 
                                name="notifications"
                                class="w-full px-4 py-3 bg-dark-surface border border-dark-border rounded-xl text-white focus:outline-none focus:border-primary input-glow transition-all duration-200"
                            >
                                <option value="true">🔔 Enabled</option>
                                <option value="false">🔕 Disabled</option>
                            </select>
                        </div>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="flex flex-col sm:flex-row gap-4 justify-end">
                    <button 
                        type="button" 
                        onclick="resetForm()"
                        class="px-6 py-3 bg-gray-600 hover:bg-gray-700 text-white font-semibold rounded-xl transition-all duration-200"
                    >
                        🔄 Reset Changes
                    </button>
                    <button 
                        type="submit" 
                        id="saveBtn"
                        class="px-8 py-3 bg-gradient-to-r from-primary to-primary-dark text-white font-semibold rounded-xl hover:from-primary-dark hover:to-purple-800 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 focus:ring-offset-dark-surface transition-all duration-200 transform hover:scale-105"
                    >
                        <span id="saveText">💾 Save Changes</span>
                        <span id="saveSpinner" class="hidden">
                            <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white inline" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                            Saving...
                        </span>
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Success/Error Toast -->
    <div id="toast" class="fixed top-4 right-4 z-50 hidden">
        <div id="toastContent" class="bg-green-500 text-white px-6 py-3 rounded-lg shadow-lg flex items-center">
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
            </svg>
            <span id="toastMessage">Success!</span>
        </div>
    </div>

    <script src="profile.js"></script>
</body>
</html>
