using UnityEngine;

public class CameraController : MonoBehaviour
{
    public Transform target;
    public float distance = 10.0f;
    public float height = 5.0f;
    public float rotationDamping = 3.0f;

    public enum CameraView { Cockpit, ThirdPerson, Cinematic }
    public CameraView currentView = CameraView.ThirdPerson;

    void LateUpdate()
    {
        if (!target) return;

        switch (currentView)
        {
            case CameraView.ThirdPerson:
                // Calculate the desired rotation and position
                float wantedRotationAngle = target.eulerAngles.y;
                float wantedHeight = target.position.y + height;

                float currentRotationAngle = transform.eulerAngles.y;
                float currentHeight = transform.position.y;

                // Damp the rotation around the y-axis
                currentRotationAngle = Mathf.LerpAngle(currentRotationAngle, wantedRotationAngle, rotationDamping * Time.deltaTime);

                // Damp the height
                currentHeight = Mathf.Lerp(currentHeight, wantedHeight, rotationDamping * Time.deltaTime);

                // Convert the angle into a rotation
                Quaternion currentRotation = Quaternion.Euler(0, currentRotationAngle, 0);

                // Set the position of the camera on the x-z plane to:
                // distance meters behind the target
                transform.position = target.position;
                transform.position -= currentRotation * Vector3.forward * distance;

                // Set the height of the camera
                transform.position = new Vector3(transform.position.x, currentHeight, transform.position.z);

                // Always look at the target
                transform.LookAt(target);
                break;

            case CameraView.Cockpit:
                // Implement cockpit view logic here (e.g., attach camera to car's interior)
                break;

            case CameraView.Cinematic:
                // Implement cinematic camera logic here (e.g., free camera movement)
                break;
        }
    }

    // You can add a method to switch between camera views based on user input
    public void SwitchCameraView(CameraView newView)
    {
        currentView = newView;
    }
}
