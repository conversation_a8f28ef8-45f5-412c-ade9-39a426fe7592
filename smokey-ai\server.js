const express = require('express');
const fs = require('fs').promises;
const path = require('path');
const cors = require('cors');
const bcrypt = require('bcrypt');

const app = express();
const PORT = process.env.PORT || 3000;
const USERS_FILE = path.join(__dirname, 'users.json');

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.static('.')); // Serve static files

// Initialize users file if it doesn't exist
async function initializeUsersFile() {
    try {
        await fs.access(USERS_FILE);
    } catch (error) {
        // File doesn't exist, create it
        const initialData = { users: [] };
        await fs.writeFile(USERS_FILE, JSON.stringify(initialData, null, 2));
        console.log('✅ Created users.json file');
    }
}

// Load users from file
async function loadUsers() {
    try {
        const data = await fs.readFile(USERS_FILE, 'utf8');
        return JSON.parse(data);
    } catch (error) {
        console.error('Error loading users:', error);
        return { users: [] };
    }
}

// Save users to file
async function saveUsers(usersData) {
    try {
        await fs.writeFile(USERS_FILE, JSON.stringify(usersData, null, 2));
        console.log('✅ Users saved to file');
    } catch (error) {
        console.error('Error saving users:', error);
        throw error;
    }
}

// Validate email format
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

// Validate password strength
function isValidPassword(password) {
    return password && password.length >= 6;
}

// Routes

// Serve the login page
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'login.html'));
});

// Register/Login endpoint
app.post('/api/auth/login', async (req, res) => {
    try {
        const { email, password, remember } = req.body;

        // Validation
        if (!email || !password) {
            return res.status(400).json({
                success: false,
                message: 'Email and password are required'
            });
        }

        if (!isValidEmail(email)) {
            return res.status(400).json({
                success: false,
                message: 'Please enter a valid email address'
            });
        }

        if (!isValidPassword(password)) {
            return res.status(400).json({
                success: false,
                message: 'Password must be at least 6 characters long'
            });
        }

        // Load existing users
        const usersData = await loadUsers();
        const existingUser = usersData.users.find(
            user => user.email.toLowerCase() === email.toLowerCase()
        );

        if (existingUser) {
            // User exists, verify password
            const isPasswordValid = await bcrypt.compare(password, existingUser.password);
            
            if (isPasswordValid) {
                // Update last login
                existingUser.lastLogin = new Date().toISOString();
                await saveUsers(usersData);

                res.json({
                    success: true,
                    message: 'Login successful',
                    user: {
                        id: existingUser.id,
                        email: existingUser.email,
                        createdAt: existingUser.createdAt,
                        lastLogin: existingUser.lastLogin
                    }
                });
            } else {
                res.status(401).json({
                    success: false,
                    message: 'Invalid credentials'
                });
            }
        } else {
            // Create new user
            const hashedPassword = await bcrypt.hash(password, 10);
            const newUser = {
                id: Date.now(),
                email: email.toLowerCase(),
                password: hashedPassword,
                createdAt: new Date().toISOString(),
                lastLogin: new Date().toISOString()
            };

            usersData.users.push(newUser);
            await saveUsers(usersData);

            console.log(`✅ New user created: ${email}`);

            res.status(201).json({
                success: true,
                message: 'Account created and logged in successfully',
                user: {
                    id: newUser.id,
                    email: newUser.email,
                    createdAt: newUser.createdAt,
                    lastLogin: newUser.lastLogin
                }
            });
        }

    } catch (error) {
        console.error('Login error:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error'
        });
    }
});

// Get all users (for debugging - remove in production)
app.get('/api/users', async (req, res) => {
    try {
        const usersData = await loadUsers();
        // Remove passwords from response
        const safeUsers = usersData.users.map(user => ({
            id: user.id,
            email: user.email,
            createdAt: user.createdAt,
            lastLogin: user.lastLogin
        }));
        
        res.json({
            success: true,
            users: safeUsers,
            count: safeUsers.length
        });
    } catch (error) {
        console.error('Error fetching users:', error);
        res.status(500).json({
            success: false,
            message: 'Error fetching users'
        });
    }
});

// Delete all users (for debugging - remove in production)
app.delete('/api/users', async (req, res) => {
    try {
        const emptyData = { users: [] };
        await saveUsers(emptyData);
        
        res.json({
            success: true,
            message: 'All users deleted'
        });
    } catch (error) {
        console.error('Error deleting users:', error);
        res.status(500).json({
            success: false,
            message: 'Error deleting users'
        });
    }
});

// Error handling middleware
app.use((err, req, res, next) => {
    console.error('Unhandled error:', err);
    res.status(500).json({
        success: false,
        message: 'Something went wrong!'
    });
});

// 404 handler
app.use((req, res) => {
    res.status(404).json({
        success: false,
        message: 'Endpoint not found'
    });
});

// Start server
async function startServer() {
    try {
        await initializeUsersFile();
        
        app.listen(PORT, () => {
            console.log(`🚀 Server running on http://localhost:${PORT}`);
            console.log(`📁 Users file: ${USERS_FILE}`);
            console.log(`🌐 Login page: http://localhost:${PORT}`);
            console.log(`📊 API endpoints:`);
            console.log(`   POST /api/auth/login - Login/Register`);
            console.log(`   GET  /api/users - View all users`);
            console.log(`   DELETE /api/users - Clear all users`);
        });
    } catch (error) {
        console.error('Failed to start server:', error);
        process.exit(1);
    }
}

startServer();
