/* Landing Page Styles */
.landing-page {
    background: linear-gradient(to bottom, #1e272e, #000);
    height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
    text-align: center;
    color: #fff;
}

.landing-content {
    padding: 20px;
}

.landing-title {
    font-size: 3em;
    text-shadow: 0 0 10px #0abde3;
    margin-bottom: 10px;
}

.landing-subtitle {
    font-size: 1.5em;
    opacity: 0.8;
}

.scroll-down {
    margin-top: 20px;
    display: inline-block;
    color: #0abde3;
    text-decoration: none;
    font-size: 1.2em;
    animation: bounce 2s infinite;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-20px);
    }
    60% {
        transform: translateY(-10px);
    }
}

body {
    background-color: #1e272e;
    font-family: 'Orbitron', sans-serif;
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    margin: 0;
    color: #fff;
}

.calculator {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 20px;
    box-shadow: 0 4px 30px rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(5px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    padding: 20px;
    width: 320px;
    text-align: center;
}

.title {
    font-size: 2em;
    margin-bottom: 20px;
    text-shadow: 0 0 10px #0abde3;
}

.display {
    margin-bottom: 20px;
}

.display input {
    width: 100%;
    padding: 15px;
    font-size: 2em;
    text-align: right;
    border: none;
    background: rgba(255, 255, 255, 0.1);
    color: #fff;
    border-radius: 10px;
    box-shadow: 0 0 10px rgba(0, 189, 227, 0.3);
    font-family: 'Orbitron', sans-serif;
}

.buttons {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    grid-gap: 10px;
}

.buttons button {
    padding: 15px;
    font-size: 1.5em;
    border: none;
    background: rgba(255, 255, 255, 0.05);
    color: #fff;
    border-radius: 10px;
    box-shadow: 0 0 10px rgba(0, 189, 227, 0.3);
    cursor: pointer;
    transition: transform 0.2s, box-shadow 0.2s;
}

.buttons button:hover {
    transform: scale(1.05);
    box-shadow: 0 0 15px #0abde3;
}

.buttons button:active {
    transform: scale(0.95);
    box-shadow: 0 0 5px #0abde3;
}

.buttons .equals {
    grid-column: span 1;
    background-color: #0abde3;
    color: #1e272e;
}

.buttons .operator {
    color: #0abde3;
}

.toggle {
    margin-top: 20px;
}

.toggle input[type="checkbox"] {
    display: none;
}

.toggle label {
    cursor: pointer;
    padding: 10px 20px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 10px;
    box-shadow: 0 0 10px rgba(0, 189, 227, 0.3);
}

/* Dark Mode Styles */
body.dark-mode {
    background-color: #000;
    color: #fff;
}

body.dark-mode .calculator {
    background: rgba(0, 0, 0, 0.5);
    box-shadow: 0 4px 30px rgba(0, 0, 0, 0.8);
    border: 1px solid rgba(0, 0, 0, 0.2);
}

body.dark-mode .display input {
    background: rgba(0, 0, 0, 0.3);
    box-shadow: 0 0 10px rgba(0, 189, 227, 0.5);
    color: #fff;
}

body.dark-mode .buttons button {
    background: rgba(0, 0, 0, 0.3);
    box-shadow: 0 0 10px rgba(0, 189, 227, 0.5);
    color: #fff;
}

body.dark-mode .buttons button:hover {
    box-shadow: 0 0 15px #0abde3;
}

body.dark-mode .toggle label {
    background: rgba(0, 0, 0, 0.3);
    box-shadow: 0 0 10px rgba(0, 189, 227, 0.5);
    color: #fff;
}
