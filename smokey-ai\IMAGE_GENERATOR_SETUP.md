# 🎨 Image Generator Setup Guide

Complete guide to integrate AI image generation into Smokey AI v1.

## 🚀 Quick Setup

### 1. **Add Your API Key**

Open `config.js` and replace `YOUR_API_KEY_HERE` with your actual API key:

```javascript
imageGeneration: {
    apiKey: 'sk-your-actual-api-key-here',
    provider: 'openai', // or 'stability', 'midjourney', etc.
    // ... rest of config
}
```

### 2. **Choose Your Provider**

**Option A: OpenAI DALL-E 3** (Recommended)
```javascript
provider: 'openai',
apiKey: 'sk-your-openai-key',
```

**Option B: Stability AI**
```javascript
provider: 'stability',
apiKey: 'sk-your-stability-key',
```

**Option C: Custom API**
```javascript
provider: 'custom',
apiKey: 'your-custom-key',
endpoints: {
    custom: 'https://your-api-endpoint.com/generate'
}
```

## 🔧 Implementation Options

### **Option 1: OpenAI DALL-E Integration**

Replace the `callImageGenerationAPI` method in `app.js`:

```javascript
async callImageGenerationAPI(prompt, style, size, quality) {
    const config = getConfig();
    
    if (!config.imageGeneration.enabled) {
        throw new Error('Image generation not configured');
    }
    
    const enhancedPrompt = this.enhancePromptWithStyle(prompt, style);
    
    const response = await fetch('https://api.openai.com/v1/images/generations', {
        method: 'POST',
        headers: {
            'Authorization': `Bearer ${config.imageGeneration.apiKey}`,
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            model: 'dall-e-3',
            prompt: enhancedPrompt,
            size: size,
            quality: quality,
            n: 1
        })
    });
    
    if (!response.ok) {
        throw new Error(`API Error: ${response.status}`);
    }
    
    const data = await response.json();
    
    return {
        url: data.data[0].url,
        prompt: enhancedPrompt,
        size: size,
        quality: quality,
        timestamp: new Date().toISOString(),
        id: Date.now()
    };
}
```

### **Option 2: Stability AI Integration**

```javascript
async callImageGenerationAPI(prompt, style, size, quality) {
    const config = getConfig();
    const enhancedPrompt = this.enhancePromptWithStyle(prompt, style);
    
    const [width, height] = size.split('x').map(Number);
    
    const response = await fetch('https://api.stability.ai/v1/generation/stable-diffusion-xl-1024-v1-0/text-to-image', {
        method: 'POST',
        headers: {
            'Authorization': `Bearer ${config.imageGeneration.apiKey}`,
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            text_prompts: [{ text: enhancedPrompt }],
            cfg_scale: 7,
            width: width,
            height: height,
            samples: 1,
            steps: 30
        })
    });
    
    const data = await response.json();
    
    // Convert base64 to blob URL
    const base64Image = data.artifacts[0].base64;
    const blob = this.base64ToBlob(base64Image, 'image/png');
    const url = URL.createObjectURL(blob);
    
    return {
        url: url,
        prompt: enhancedPrompt,
        size: size,
        quality: quality,
        timestamp: new Date().toISOString(),
        id: Date.now()
    };
}

base64ToBlob(base64, mimeType) {
    const byteCharacters = atob(base64);
    const byteNumbers = new Array(byteCharacters.length);
    for (let i = 0; i < byteCharacters.length; i++) {
        byteNumbers[i] = byteCharacters.charCodeAt(i);
    }
    const byteArray = new Uint8Array(byteNumbers);
    return new Blob([byteArray], { type: mimeType });
}
```

## 🔑 Getting API Keys

### **OpenAI DALL-E**
1. Go to [OpenAI Platform](https://platform.openai.com/)
2. Sign up/Login
3. Navigate to API Keys
4. Create new secret key
5. Copy the key (starts with `sk-`)

**Pricing:** ~$0.040 per image (1024x1024)

### **Stability AI**
1. Go to [Stability AI](https://platform.stability.ai/)
2. Sign up/Login
3. Go to Account → API Keys
4. Generate new key
5. Copy the key (starts with `sk-`)

**Pricing:** ~$0.020 per image (1024x1024)

### **Alternative APIs**
- **Replicate**: Various models, pay-per-use
- **Hugging Face**: Free tier available
- **Midjourney**: Discord bot integration
- **Custom**: Your own hosted model

## 🛡️ Security Best Practices

### **Environment Variables** (Recommended)
Instead of hardcoding API keys, use environment variables:

```javascript
// In config.js
imageGeneration: {
    apiKey: process.env.IMAGE_API_KEY || 'YOUR_API_KEY_HERE',
    // ...
}
```

### **Server-Side Proxy** (Most Secure)
Create a backend endpoint to handle API calls:

```javascript
// In your backend (Node.js example)
app.post('/api/generate-image', async (req, res) => {
    const { prompt, style, size, quality } = req.body;
    
    // Call image API with server-side key
    const result = await callImageAPI(prompt, style, size, quality);
    
    res.json(result);
});
```

Then update the frontend to call your backend:

```javascript
async callImageGenerationAPI(prompt, style, size, quality) {
    const response = await fetch('/api/generate-image', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ prompt, style, size, quality })
    });
    
    return await response.json();
}
```

## 🧪 Testing

### **Test with Placeholder**
The current implementation uses placeholder images from Lorem Picsum. This lets you test the UI without an API key.

### **Test with Real API**
1. Add your API key to `config.js`
2. Replace `simulateImageGeneration` with real API call
3. Test with simple prompts like "a red apple"

### **Debug Mode**
Enable debug mode in `config.js`:

```javascript
app: {
    debugMode: true
}
```

This will show detailed console logs for troubleshooting.

## 🎯 Features Included

✅ **Beautiful Modal UI** - Professional image generation interface
✅ **Style Presets** - 8 different art styles
✅ **Size Options** - Square, portrait, landscape
✅ **Quality Settings** - Standard and HD
✅ **Example Prompts** - Quick-start templates
✅ **Real-time Counter** - Character count with warnings
✅ **Loading States** - Smooth UX during generation
✅ **Error Handling** - Graceful failure management
✅ **Image Actions** - Open, copy, regenerate
✅ **Memory Integration** - Saves generation history
✅ **User-specific** - Each user's images are private

## 🚀 Ready to Use!

1. **Add your API key** to `config.js`
2. **Choose your provider** (OpenAI recommended)
3. **Replace the simulation** with real API call
4. **Test and enjoy!** 🎨

The image generator is now fully integrated into Smokey AI v1 with a beautiful UI and all the features you need!
