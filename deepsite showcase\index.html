<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>My Capabilities Showcase</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        /* Custom CSS for elements that need more precise control */
        .gradient-bg {
            background: linear-gradient(135deg, #6e8efb, #a777e3);
        }
        .card-hover:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }
        .skill-bar {
            height: 10px;
            border-radius: 5px;
            background-color: #e5e7eb;
            overflow: hidden;
        }
        .skill-progress {
            height: 100%;
            border-radius: 5px;
            background: linear-gradient(90deg, #6e8efb, #a777e3);
            transition: width 1s ease-in-out;
        }
        .project-card {
            perspective: 1000px;
        }
        .project-inner {
            transition: transform 0.6s;
            transform-style: preserve-3d;
        }
        .project-card:hover .project-inner {
            transform: rotateY(180deg);
        }
        .project-front, .project-back {
            backface-visibility: hidden;
            position: absolute;
            width: 100%;
            height: 100%;
        }
        .project-back {
            transform: rotateY(180deg);
        }
        .nav-link {
            position: relative;
        }
        .nav-link::after {
            content: '';
            position: absolute;
            width: 0;
            height: 2px;
            bottom: 0;
            left: 0;
            background-color: white;
            transition: width 0.3s ease;
        }
        .nav-link:hover::after {
            width: 100%;
        }
        @keyframes float {
            0%, 100% {
                transform: translateY(0);
            }
            50% {
                transform: translateY(-20px);
            }
        }
        .floating {
            animation: float 6s ease-in-out infinite;
        }
    </style>
</head>
<body class="font-sans bg-gray-50 text-gray-800">
    <!-- Navigation -->
    <nav class="bg-indigo-900 text-white shadow-lg fixed w-full z-10">
        <div class="container mx-auto px-6 py-3 flex justify-between items-center">
            <a href="#" class="text-2xl font-bold">My<span class="text-purple-300">Capabilities</span></a>
            <div class="hidden md:flex space-x-8">
                <a href="#home" class="nav-link hover:text-purple-300">Home</a>
                <a href="#skills" class="nav-link hover:text-purple-300">Skills</a>
                <a href="#projects" class="nav-link hover:text-purple-300">Projects</a>
                <a href="#contact" class="nav-link hover:text-purple-300">Contact</a>
            </div>
            <button id="mobile-menu-button" class="md:hidden focus:outline-none">
                <i class="fas fa-bars text-2xl"></i>
            </button>
        </div>
        <!-- Mobile menu -->
        <div id="mobile-menu" class="hidden md:hidden bg-indigo-800 px-6 py-3">
            <a href="#home" class="block py-2 hover:text-purple-300">Home</a>
            <a href="#skills" class="block py-2 hover:text-purple-300">Skills</a>
            <a href="#projects" class="block py-2 hover:text-purple-300">Projects</a>
            <a href="#contact" class="block py-2 hover:text-purple-300">Contact</a>
        </div>
    </nav>

    <!-- Hero Section -->
    <section id="home" class="gradient-bg text-white pt-32 pb-20 md:pt-40 md:pb-28">
        <div class="container mx-auto px-6 flex flex-col md:flex-row items-center">
            <div class="md:w-1/2 mb-10 md:mb-0">
                <h1 class="text-4xl md:text-6xl font-bold mb-6">Hi, I'm <span class="text-yellow-300">Developer</span></h1>
                <p class="text-xl mb-8">I create beautiful, responsive websites with modern technologies.</p>
                <div class="flex space-x-4">
                    <a href="#contact" class="bg-white text-indigo-900 px-6 py-3 rounded-full font-semibold hover:bg-gray-100 transition duration-300">Hire Me</a>
                    <a href="#projects" class="border-2 border-white px-6 py-3 rounded-full font-semibold hover:bg-white hover:text-indigo-900 transition duration-300">My Work</a>
                </div>
            </div>
            <div class="md:w-1/2 flex justify-center">
                <div class="relative w-64 h-64 md:w-80 md:h-80">
                    <img src="https://via.placeholder.com/300" alt="Profile" class="rounded-full border-4 border-white shadow-xl floating">
                    <div class="absolute -bottom-5 -right-5 bg-yellow-400 text-indigo-900 p-4 rounded-full shadow-lg">
                        <i class="fas fa-code text-3xl"></i>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Skills Section -->
    <section id="skills" class="py-20 bg-white">
        <div class="container mx-auto px-6">
            <h2 class="text-3xl md:text-4xl font-bold text-center mb-16">My <span class="text-indigo-600">Skills</span></h2>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-12">
                <!-- Technical Skills -->
                <div>
                    <h3 class="text-2xl font-semibold mb-6 flex items-center">
                        <i class="fas fa-laptop-code text-indigo-600 mr-3"></i> Technical Skills
                    </h3>
                    <div class="space-y-6">
                        <div>
                            <div class="flex justify-between mb-2">
                                <span>HTML/CSS</span>
                                <span>95%</span>
                            </div>
                            <div class="skill-bar">
                                <div class="skill-progress" style="width: 95%"></div>
                            </div>
                        </div>
                        <div>
                            <div class="flex justify-between mb-2">
                                <span>JavaScript</span>
                                <span>90%</span>
                            </div>
                            <div class="skill-bar">
                                <div class="skill-progress" style="width: 90%"></div>
                            </div>
                        </div>
                        <div>
                            <div class="flex justify-between mb-2">
                                <span>React</span>
                                <span>85%</span>
                            </div>
                            <div class="skill-bar">
                                <div class="skill-progress" style="width: 85%"></div>
                            </div>
                        </div>
                        <div>
                            <div class="flex justify-between mb-2">
                                <span>Node.js</span>
                                <span>80%</span>
                            </div>
                            <div class="skill-bar">
                                <div class="skill-progress" style="width: 80%"></div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Soft Skills -->
                <div>
                    <h3 class="text-2xl font-semibold mb-6 flex items-center">
                        <i class="fas fa-users text-indigo-600 mr-3"></i> Soft Skills
                    </h3>
                    <div class="grid grid-cols-2 gap-4">
                        <div class="bg-gray-100 p-4 rounded-lg flex items-center">
                            <i class="fas fa-comments text-indigo-600 mr-3 text-xl"></i>
                            <span>Communication</span>
                        </div>
                        <div class="bg-gray-100 p-4 rounded-lg flex items-center">
                            <i class="fas fa-lightbulb text-indigo-600 mr-3 text-xl"></i>
                            <span>Problem Solving</span>
                        </div>
                        <div class="bg-gray-100 p-4 rounded-lg flex items-center">
                            <i class="fas fa-users-cog text-indigo-600 mr-3 text-xl"></i>
                            <span>Teamwork</span>
                        </div>
                        <div class="bg-gray-100 p-4 rounded-lg flex items-center">
                            <i class="fas fa-clock text-indigo-600 mr-3 text-xl"></i>
                            <span>Time Management</span>
                        </div>
                        <div class="bg-gray-100 p-4 rounded-lg flex items-center">
                            <i class="fas fa-brain text-indigo-600 mr-3 text-xl"></i>
                            <span>Creativity</span>
                        </div>
                        <div class="bg-gray-100 p-4 rounded-lg flex items-center">
                            <i class="fas fa-tasks text-indigo-600 mr-3 text-xl"></i>
                            <span>Adaptability</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Tools Section -->
            <div class="mt-16">
                <h3 class="text-2xl font-semibold mb-8 flex items-center">
                    <i class="fas fa-tools text-indigo-600 mr-3"></i> Tools & Technologies
                </h3>
                <div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-6 gap-6">
                    <div class="flex flex-col items-center p-4 bg-gray-100 rounded-lg hover:bg-indigo-100 transition duration-300">
                        <i class="fab fa-html5 text-4xl text-orange-500 mb-2"></i>
                        <span>HTML5</span>
                    </div>
                    <div class="flex flex-col items-center p-4 bg-gray-100 rounded-lg hover:bg-indigo-100 transition duration-300">
                        <i class="fab fa-css3-alt text-4xl text-blue-500 mb-2"></i>
                        <span>CSS3</span>
                    </div>
                    <div class="flex flex-col items-center p-4 bg-gray-100 rounded-lg hover:bg-indigo-100 transition duration-300">
                        <i class="fab fa-js text-4xl text-yellow-500 mb-2"></i>
                        <span>JavaScript</span>
                    </div>
                    <div class="flex flex-col items-center p-4 bg-gray-100 rounded-lg hover:bg-indigo-100 transition duration-300">
                        <i class="fab fa-react text-4xl text-blue-400 mb-2"></i>
                        <span>React</span>
                    </div>
                    <div class="flex flex-col items-center p-4 bg-gray-100 rounded-lg hover:bg-indigo-100 transition duration-300">
                        <i class="fab fa-node-js text-4xl text-green-500 mb-2"></i>
                        <span>Node.js</span>
                    </div>
                    <div class="flex flex-col items-center p-4 bg-gray-100 rounded-lg hover:bg-indigo-100 transition duration-300">
                        <i class="fab fa-git-alt text-4xl text-orange-600 mb-2"></i>
                        <span>Git</span>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Projects Section -->
    <section id="projects" class="py-20 bg-gray-100">
        <div class="container mx-auto px-6">
            <h2 class="text-3xl md:text-4xl font-bold text-center mb-16">Featured <span class="text-indigo-600">Projects</span></h2>
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                <!-- Project 1 -->
                <div class="project-card h-80">
                    <div class="project-inner relative w-full h-full rounded-xl overflow-hidden shadow-lg">
                        <div class="project-front bg-white h-full">
                            <img src="https://via.placeholder.com/400x300" alt="Project 1" class="w-full h-40 object-cover">
                            <div class="p-6">
                                <h3 class="text-xl font-bold mb-2">E-commerce Website</h3>
                                <p class="text-gray-600 mb-4">A fully responsive online store with cart functionality.</p>
                                <div class="flex flex-wrap gap-2">
                                    <span class="bg-indigo-100 text-indigo-800 text-xs px-3 py-1 rounded-full">React</span>
                                    <span class="bg-indigo-100 text-indigo-800 text-xs px-3 py-1 rounded-full">Node.js</span>
                                    <span class="bg-indigo-100 text-indigo-800 text-xs px-3 py-1 rounded-full">MongoDB</span>
                                </div>
                            </div>
                        </div>
                        <div class="project-back bg-indigo-900 text-white p-6 flex flex-col justify-center items-center">
                            <h3 class="text-xl font-bold mb-4">E-commerce Website</h3>
                            <p class="text-center mb-6">Built with modern technologies including React for the frontend and Node.js for the backend.</p>
                            <div class="flex space-x-4">
                                <a href="#" class="bg-white text-indigo-900 px-4 py-2 rounded-full font-semibold text-sm">Live Demo</a>
                                <a href="#" class="border border-white px-4 py-2 rounded-full font-semibold text-sm">Code</a>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Project 2 -->
                <div class="project-card h-80">
                    <div class="project-inner relative w-full h-full rounded-xl overflow-hidden shadow-lg">
                        <div class="project-front bg-white h-full">
                            <img src="https://via.placeholder.com/400x300" alt="Project 2" class="w-full h-40 object-cover">
                            <div class="p-6">
                                <h3 class="text-xl font-bold mb-2">Task Management App</h3>
                                <p class="text-gray-600 mb-4">A productivity app to organize your daily tasks.</p>
                                <div class="flex flex-wrap gap-2">
                                    <span class="bg-indigo-100 text-indigo-800 text-xs px-3 py-1 rounded-full">Vue.js</span>
                                    <span class="bg-indigo-100 text-indigo-800 text-xs px-3 py-1 rounded-full">Firebase</span>
                                </div>
                            </div>
                        </div>
                        <div class="project-back bg-indigo-900 text-white p-6 flex flex-col justify-center items-center">
                            <h3 class="text-xl font-bold mb-4">Task Management App</h3>
                            <p class="text-center mb-6">A drag-and-drop interface with real-time updates using Firebase as the backend.</p>
                            <div class="flex space-x-4">
                                <a href="#" class="bg-white text-indigo-900 px-4 py-2 rounded-full font-semibold text-sm">Live Demo</a>
                                <a href="#" class="border border-white px-4 py-2 rounded-full font-semibold text-sm">Code</a>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Project 3 -->
                <div class="project-card h-80">
                    <div class="project-inner relative w-full h-full rounded-xl overflow-hidden shadow-lg">
                        <div class="project-front bg-white h-full">
                            <img src="https://via.placeholder.com/400x300" alt="Project 3" class="w-full h-40 object-cover">
                            <div class="p-6">
                                <h3 class="text-xl font-bold mb-2">Weather Dashboard</h3>
                                <p class="text-gray-600 mb-4">Real-time weather information with 5-day forecast.</p>
                                <div class="flex flex-wrap gap-2">
                                    <span class="bg-indigo-100 text-indigo-800 text-xs px-3 py-1 rounded-full">JavaScript</span>
                                    <span class="bg-indigo-100 text-indigo-800 text-xs px-3 py-1 rounded-full">API</span>
                                </div>
                            </div>
                        </div>
                        <div class="project-back bg-indigo-900 text-white p-6 flex flex-col justify-center items-center">
                            <h3 class="text-xl font-bold mb-4">Weather Dashboard</h3>
                            <p class="text-center mb-6">Integrated with the OpenWeather API to provide accurate weather forecasts for any location.</p>
                            <div class="flex space-x-4">
                                <a href="#" class="bg-white text-indigo-900 px-4 py-2 rounded-full font-semibold text-sm">Live Demo</a>
                                <a href="#" class="border border-white px-4 py-2 rounded-full font-semibold text-sm">Code</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="text-center mt-12">
                <a href="#" class="inline-block bg-indigo-600 text-white px-8 py-3 rounded-full font-semibold hover:bg-indigo-700 transition duration-300">
                    View All Projects <i class="fas fa-arrow-right ml-2"></i>
                </a>
            </div>
        </div>
    </section>

    <!-- Contact Section -->
    <section id="contact" class="py-20 bg-white">
        <div class="container mx-auto px-6">
            <h2 class="text-3xl md:text-4xl font-bold text-center mb-16">Get In <span class="text-indigo-600">Touch</span></h2>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-12">
                <!-- Contact Form -->
                <div class="bg-gray-50 p-8 rounded-xl shadow-lg">
                    <h3 class="text-2xl font-semibold mb-6">Send Me a Message</h3>
                    <form id="contactForm" class="space-y-6">
                        <div>
                            <label for="name" class="block text-gray-700 mb-2">Name</label>
                            <input type="text" id="name" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500">
                        </div>
                        <div>
                            <label for="email" class="block text-gray-700 mb-2">Email</label>
                            <input type="email" id="email" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500">
                        </div>
                        <div>
                            <label for="subject" class="block text-gray-700 mb-2">Subject</label>
                            <input type="text" id="subject" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500">
                        </div>
                        <div>
                            <label for="message" class="block text-gray-700 mb-2">Message</label>
                            <textarea id="message" rows="5" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500"></textarea>
                        </div>
                        <button type="submit" class="w-full bg-indigo-600 text-white py-3 rounded-lg font-semibold hover:bg-indigo-700 transition duration-300">
                            Send Message <i class="fas fa-paper-plane ml-2"></i>
                        </button>
                    </form>
                </div>
                
                <!-- Contact Info -->
                <div>
                    <h3 class="text-2xl font-semibold mb-6">Contact Information</h3>
                    <p class="text-gray-600 mb-8">Feel free to reach out to me for any questions or opportunities. I'll get back to you as soon as possible.</p>
                    
                    <div class="space-y-6">
                        <div class="flex items-start">
                            <div class="bg-indigo-100 p-3 rounded-full mr-4">
                                <i class="fas fa-map-marker-alt text-indigo-600"></i>
                            </div>
                            <div>
                                <h4 class="font-semibold">Location</h4>
                                <p class="text-gray-600">San Francisco, CA</p>
                            </div>
                        </div>
                        <div class="flex items-start">
                            <div class="bg-indigo-100 p-3 rounded-full mr-4">
                                <i class="fas fa-envelope text-indigo-600"></i>
                            </div>
                            <div>
                                <h4 class="font-semibold">Email</h4>
                                <p class="text-gray-600"><EMAIL></p>
                            </div>
                        </div>
                        <div class="flex items-start">
                            <div class="bg-indigo-100 p-3 rounded-full mr-4">
                                <i class="fas fa-phone text-indigo-600"></i>
                            </div>
                            <div>
                                <h4 class="font-semibold">Phone</h4>
                                <p class="text-gray-600">(*************</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mt-10">
                        <h4 class="font-semibold mb-4">Follow Me</h4>
                        <div class="flex space-x-4">
                            <a href="#" class="bg-gray-200 p-3 rounded-full hover:bg-indigo-600 hover:text-white transition duration-300">
                                <i class="fab fa-github"></i>
                            </a>
                            <a href="#" class="bg-gray-200 p-3 rounded-full hover:bg-indigo-600 hover:text-white transition duration-300">
                                <i class="fab fa-linkedin-in"></i>
                            </a>
                            <a href="#" class="bg-gray-200 p-3 rounded-full hover:bg-indigo-600 hover:text-white transition duration-300">
                                <i class="fab fa-twitter"></i>
                            </a>
                            <a href="#" class="bg-gray-200 p-3 rounded-full hover:bg-indigo-600 hover:text-white transition duration-300">
                                <i class="fab fa-instagram"></i>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-gray-900 text-white py-12">
        <div class="container mx-auto px-6">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <div>
                    <h3 class="text-xl font-bold mb-4">MyCapabilities</h3>
                    <p class="text-gray-400">Creating beautiful, functional websites that help businesses grow and succeed in the digital world.</p>
                </div>
                <div>
                    <h4 class="font-semibold mb-4">Quick Links</h4>
                    <ul class="space-y-2">
                        <li><a href="#home" class="text-gray-400 hover:text-white transition duration-300">Home</a></li>
                        <li><a href="#skills" class="text-gray-400 hover:text-white transition duration-300">Skills</a></li>
                        <li><a href="#projects" class="text-gray-400 hover:text-white transition duration-300">Projects</a></li>
                        <li><a href="#contact" class="text-gray-400 hover:text-white transition duration-300">Contact</a></li>
                    </ul>
                </div>
                <div>
                    <h4 class="font-semibold mb-4">Services</h4>
                    <ul class="space-y-2">
                        <li><a href="#" class="text-gray-400 hover:text-white transition duration-300">Web Development</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition duration-300">UI/UX Design</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition duration-300">Mobile Apps</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition duration-300">SEO Optimization</a></li>
                    </ul>
                </div>
                <div>
                    <h4 class="font-semibold mb-4">Newsletter</h4>
                    <p class="text-gray-400 mb-4">Subscribe to get updates on my latest projects and articles.</p>
                    <div class="flex">
                        <input type="email" placeholder="Your email" class="px-4 py-2 rounded-l-lg focus:outline-none text-gray-900 w-full">
                        <button class="bg-indigo-600 px-4 py-2 rounded-r-lg hover:bg-indigo-700 transition duration-300">
                            <i class="fas fa-paper-plane"></i>
                        </button>
                    </div>
                </div>
            </div>
            <div class="border-t border-gray-800 mt-12 pt-8 flex flex-col md:flex-row justify-between items-center">
                <p class="text-gray-400">© 2023 MyCapabilities. All rights reserved.</p>
                <div class="flex space-x-6 mt-4 md:mt-0">
                    <a href="#" class="text-gray-400 hover:text-white transition duration-300">Privacy Policy</a>
                    <a href="#" class="text-gray-400 hover:text-white transition duration-300">Terms of Service</a>
                </div>
            </div>
        </div>
    </footer>

    <!-- Back to Top Button -->
    <button id="backToTop" class="fixed bottom-8 right-8 bg-indigo-600 text-white p-3 rounded-full shadow-lg opacity-0 invisible transition-all duration-300">
        <i class="fas fa-arrow-up"></i>
    </button>

    <script>
        // Mobile menu toggle
        const mobileMenuButton = document.getElementById('mobile-menu-button');
        const mobileMenu = document.getElementById('mobile-menu');
        
        mobileMenuButton.addEventListener('click', () => {
            mobileMenu.classList.toggle('hidden');
        });
        
        // Smooth scrolling for navigation links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                
                const targetId = this.getAttribute('href');
                const targetElement = document.querySelector(targetId);
                
                if (targetElement) {
                    window.scrollTo({
                        top: targetElement.offsetTop - 80,
                        behavior: 'smooth'
                    });
                    
                    // Close mobile menu if open
                    if (!mobileMenu.classList.contains('hidden')) {
                        mobileMenu.classList.add('hidden');
                    }
                }
            });
        });
        
        // Back to top button
        const backToTopButton = document.getElementById('backToTop');
        
        window.addEventListener('scroll', () => {
            if (window.pageYOffset > 300) {
                backToTopButton.classList.remove('opacity-0', 'invisible');
                backToTopButton.classList.add('opacity-100', 'visible');
            } else {
                backToTopButton.classList.remove('opacity-100', 'visible');
                backToTopButton.classList.add('opacity-0', 'invisible');
            }
        });
        
        backToTopButton.addEventListener('click', () => {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        });
        
        // Form submission
        const contactForm = document.getElementById('contactForm');
        
        contactForm.addEventListener('submit', (e) => {
            e.preventDefault();
            
            // Get form values
            const name = document.getElementById('name').value;
            const email = document.getElementById('email').value;
            const subject = document.getElementById('subject').value;
            const message = document.getElementById('message').value;
            
            // Here you would typically send the data to a server
            console.log({ name, email, subject, message });
            
            // Show success message
            alert('Thank you for your message! I will get back to you soon.');
            
            // Reset form
            contactForm.reset();
        });
        
        // Animate skill bars on scroll
        const animateSkillBars = () => {
            const skillBars = document.querySelectorAll('.skill-progress');
            const skillsSection = document.getElementById('skills');
            
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        skillBars.forEach(bar => {
                            const width = bar.style.width;
                            bar.style.width = '0';
                            setTimeout(() => {
                                bar.style.width = width;
                            }, 100);
                        });
                        observer.unobserve(entry.target);
                    }
                });
            }, { threshold: 0.5 });
            
            observer.observe(skillsSection);
        };
        
        // Initialize animations when page loads
        window.addEventListener('DOMContentLoaded', () => {
            animateSkillBars();
        });
    </script>
</body>
</html>