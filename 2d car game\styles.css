/* Basic Reset & Global Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f4f4f4;
}

a {
    text-decoration: none;
    color: #007bff;
}

a:hover {
    text-decoration: underline;
}

ul {
    list-style: none;
}

img {
    max-width: 100%;
    height: auto;
}

/* Header & Navigation */
header {
    background-color: #333;
    color: #fff;
    padding: 1rem 0;
    position: fixed;
    width: 100%;
    top: 0;
    z-index: 1000;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

header nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1100px;
    margin: 0 auto;
    padding: 0 20px;
}

header .logo a {
    font-size: 1.8rem;
    font-weight: bold;
    color: #fff;
}

header nav ul {
    display: flex;
}

header nav ul li {
    margin-left: 20px;
}

header nav ul li a {
    color: #fff;
    padding: 0.5rem 1rem;
    transition: background-color 0.3s ease;
}

header nav ul li a:hover,
header nav ul li a.active { /* For active page indication (JS needed) */
    background-color: #555;
    border-radius: 5px;
}


/* Hero Section */
.hero {
    background: url('images/hero-bg.jpg') no-repeat center center/cover; /* Add a background image */
    color: #fff;
    height: 70vh;
    display: flex;
    justify-content: center;
    align-items: center;
    text-align: center;
    padding: 0 20px;
    margin-top: 60px; /* Adjust based on header height */
}

.hero-content h1 {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.hero-content p {
    font-size: 1.2rem;
    margin-bottom: 2rem;
}

.cta-button {
    background-color: #007bff;
    color: #fff;
    padding: 0.8rem 1.5rem;
    border-radius: 5px;
    font-size: 1.1rem;
    transition: background-color 0.3s ease;
}

.cta-button:hover {
    background-color: #0056b3;
    text-decoration: none;
}

/* Content Sections */
.content-section {
    padding: 60px 20px;
    max-width: 1000px;
    margin: 0 auto;
    text-align: center;
}

.content-section h2 {
    font-size: 2.5rem;
    margin-bottom: 30px;
    color: #333;
}

.bg-light {
    background-color: #fff;
}

/* Fraud Types Grid */
.fraud-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 25px;
    text-align: left;
}

.fraud-card {
    background-color: #fff;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.fraud-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 6px 12px rgba(0,0,0,0.15);
}

.fraud-card img {
    width: 60px; /* Adjust as needed */
    height: 60px;
    margin-bottom: 15px;
    display: block;
    margin-left: auto;
    margin-right: auto;
}

.fraud-card h3 {
    font-size: 1.5rem;
    margin-bottom: 10px;
    color: #007bff;
    text-align: center;
}

.fraud-card p {
    font-size: 0.95rem;
    color: #555;
}

/* Precautions List */
.precautions-list {
    list-style: none;
    padding: 0;
    text-align: left;
}

.precautions-list li {
    background-color: #f9f9f9;
    margin-bottom: 20px;
    padding: 20px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.precautions-list .icon {
    margin-right: 20px;
    flex-shrink: 0;
}

.precautions-list .icon img {
    width: 50px; /* Adjust as needed */
    height: 50px;
}

.precautions-list .text h4 {
    font-size: 1.2rem;
    color: #333;
    margin-bottom: 5px;
}

.precautions-list .text p {
    font-size: 0.9rem;
    color: #666;
}

/* Reporting Section */
#reporting ul {
    list-style: disc;
    margin-left: 20px;
    text-align: left;
}
#reporting ul li {
    margin-bottom: 10px;
}
#reporting ul ul {
    margin-top: 5px;
    margin-left: 20px;
    list-style: circle;
}

/* Resources List */
.resource-list {
    list-style: none;
    padding: 0;
    text-align: left;
}
.resource-list li {
    margin-bottom: 10px;
}
.resource-list li a {
    font-weight: bold;
}


/* Footer */
footer {
    background-color: #222;
    color: #ccc;
    text-align: center;
    padding: 20px;
    font-size: 0.9rem;
}

footer p {
    margin-bottom: 5px;
}

footer a {
    color: #00aaff;
}

footer a:hover {
    color: #fff;
}

/* Responsive Design (Basic Example) */
@media (max-width: 768px) {
    header nav {
        flex-direction: column;
    }

    header nav ul {
        margin-top: 1rem;
        flex-direction: column;
        align-items: center;
    }

    header nav ul li {
        margin: 0.5rem 0;
    }

    .hero {
        height: auto;
        padding-top: 80px; /* Adjust for fixed header */
        padding-bottom: 40px;
    }

    .hero-content h1 {
        font-size: 2.2rem;
    }

    .content-section h2 {
        font-size: 2rem;
    }

    .fraud-grid {
        grid-template-columns: 1fr; /* Stack cards on smaller screens */
    }
}