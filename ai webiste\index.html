<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Animation Showcase</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="styles.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.11.4/gsap.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
</head>
<body>
    <!-- Loading Animation -->
    <div class="loader">
        <div class="loader-spinner"></div>
    </div>

    <!-- Dark Mode Toggle -->
    <div class="theme-toggle">
        <input type="checkbox" id="toggle">
        <label for="toggle" class="toggle-label">
            <i class="fas fa-sun"></i>
            <i class="fas fa-moon"></i>
            <span class="toggle-ball"></span>
        </label>
    </div>

    <!-- Header Section -->
    <header class="parallax-header">
        <nav>
            <ul>
                <li><a href="#home">Home</a></li>
                <li><a href="#about">About</a></li>
                <li><a href="#gallery">Gallery</a></li>
                <li><a href="#contact">Contact</a></li>
            </ul>
        </nav>
        <h1 class="title-animation">Animation Showcase</h1>
        <div class="scroll-down">
            <div class="mouse">
                <div class="scroller"></div>
            </div>
            <p>Scroll to explore</p>
        </div>
    </header>

    <!-- About Section -->
    <section id="about" class="about-section">
        <div class="about-content">
            <h2>About This Showcase</h2>
            <p>Welcome to our immersive animation experience. This platform demonstrates various web animation techniques from simple CSS transitions to complex JavaScript-powered interactions.</p>
            <p>Explore different animation styles and see how they can enhance user experience and bring websites to life.</p>
            <div class="tech-stack">
                <div class="tech-item" data-tooltip="CSS Animations">
                    <i class="fab fa-css3-alt"></i>
                </div>
                <div class="tech-item" data-tooltip="JavaScript">
                    <i class="fab fa-js"></i>
                </div>
                <div class="tech-item" data-tooltip="GSAP">
                    <i class="fas fa-bolt"></i>
                </div>
                <div class="tech-item" data-tooltip="Three.js">
                    <i class="fas fa-cube"></i>
                </div>
            </div>
        </div>
    </section>

    <!-- Gallery Section -->
    <section id="gallery" class="gallery-section">
        <h2>Animation Gallery</h2>
        <div class="animation-grid">
            <!-- Rotating Animation -->
            <div class="animation-card">
                <div class="animation-box rotate-animation">
                    <div class="rotating-element"></div>
                </div>
                <h3>Rotating Cube</h3>
                <p>CSS keyframe animation with 3D transforms</p>
                <button class="animation-control" data-animation="rotate">Toggle Speed</button>
            </div>

            <!-- Bouncing Animation -->
            <div class="animation-card">
                <div class="animation-box bounce-animation">
                    <div class="bouncing-ball"></div>
                </div>
                <h3>Bouncing Ball</h3>
                <p>CSS timing functions with translateY</p>
                <button class="animation-control" data-animation="bounce">Toggle Bounce</button>
            </div>

            <!-- Fading Animation -->
            <div class="animation-card">
                <div class="animation-box fade-animation">
                    <div class="fading-element"></div>
                </div>
                <h3>Fading Pulse</h3>
                <p>Opacity transitions with keyframes</p>
                <button class="animation-control" data-animation="fade">Toggle Fade</button>
            </div>

            <!-- Morphing Animation -->
            <div class="animation-card">
                <div class="animation-box morph-animation">
                    <div class="morphing-element"></div>
                </div>
                <h3>Morphing Shape</h3>
                <p>SVG path animation with GSAP</p>
                <button class="animation-control" data-animation="morph">Trigger Morph</button>
            </div>

            <!-- 3D Animation -->
            <div class="animation-card">
                <div class="animation-box" id="threejs-container"></div>
                <h3>3D Sphere</h3>
                <p>Three.js WebGL rendering</p>
                <button class="animation-control" data-animation="threejs">Toggle Rotation</button>
            </div>

            <!-- Particle Animation -->
            <div class="animation-card">
                <div class="animation-box particle-animation">
                    <canvas id="particle-canvas"></canvas>
                </div>
                <h3>Particle System</h3>
                <p>HTML5 Canvas animation</p>
                <button class="animation-control" data-animation="particle">Reset Particles</button>
            </div>
        </div>
    </section>

    <!-- Contact Section -->
    <section id="contact" class="contact-section">
        <h2>Get In Touch</h2>
        <form id="contactForm" class="contact-form">
            <div class="form-group">
                <input type="text" id="name" placeholder="Your Name" required>
                <div class="underline"></div>
            </div>
            <div class="form-group">
                <input type="email" id="email" placeholder="Your Email" required>
                <div class="underline"></div>
            </div>
            <div class="form-group">
                <textarea id="message" placeholder="Your Message" required></textarea>
                <div class="underline"></div>
            </div>
            <button type="submit" class="submit-btn">
                <span>Send Message</span>
                <i class="fas fa-paper-plane"></i>
            </button>
        </form>
        <div class="social-links">
            <a href="#" class="social-icon"><i class="fab fa-twitter"></i></a>
            <a href="#" class="social-icon"><i class="fab fa-github"></i></a>
            <a href="#" class="social-icon"><i class="fab fa-codepen"></i></a>
            <a href="#" class="social-icon"><i class="fab fa-linkedin"></i></a>
        </div>
    </section>

    <!-- Footer -->
    <footer>
        <div class="footer-content">
            <p>© 2023 Animation Showcase. All rights reserved.</p>
            <div class="footer-animation">
                <i class="fas fa-globe-americas spinning-globe"></i>
            </div>
            <a href="#home" class="back-to-top">
                <i class="fas fa-arrow-up"></i>
            </a>
        </div>
    </footer>

    <script src="script.js"></script>
</body>
</html>