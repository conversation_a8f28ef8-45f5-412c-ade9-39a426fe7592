# Realistic Car Driving Game

## Instructions

1.  Create a new Unity project inside this directory (`realistic-car-driving-game`).
2.  Import necessary assets for car physics and rendering (e.g., from the Unity Asset Store).
3.  Implement the core gameplay features as described in the original task.

## Setting up the Unity Project

1.  Create a new Unity project inside this directory (`realistic-car-driving-game`).
2.  Import a car model asset (e.g., from the Unity Asset Store or a 3D modeling tool).
3.  Create a new GameObject in the scene and attach the `CarMovement.cs` script to it.
4.  Add a `Rigidbody` component to the car GameObject.
5.  Create a new `Camera` in the scene and attach the `CameraController.cs` script to it.
6.  Assign the car GameObject as the target for the `CameraController`.
