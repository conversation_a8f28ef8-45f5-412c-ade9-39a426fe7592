/* styles.css */
:root {
    /* Dark theme colors */
    --bg-primary: #121212;
    --bg-secondary: #1e1e1e;
    --text-primary: #ffffff;
    --text-secondary: #b3b3b3;
    --accent-primary: #7b2cbf;
    --accent-secondary: #3a86ff;
    --success: #4cc9f0;
    --warning: #f8961e;
    --danger: #f94144;
    --glass-bg: rgba(30, 30, 30, 0.6);
    --glass-border: rgba(255, 255, 255, 0.1);
    --shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

[data-theme="light"] {
    --bg-primary: #f8f9fa;
    --bg-secondary: #ffffff;
    --text-primary: #212529;
    --text-secondary: #495057;
    --glass-bg: rgba(255, 255, 255, 0.6);
    --glass-border: rgba(0, 0, 0, 0.1);
    --shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Poppins', sans-serif;
    background-color: var(--bg-primary);
    color: var(--text-primary);
    min-height: 100vh;
    transition: var(--transition);
}

/* Main layout */
.app-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 1rem;
    padding-bottom: 80px;
}

/* Navigation */
.main-nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 2rem;
    background-color: var(--bg-secondary);
    box-shadow: var(--shadow);
    position: sticky;
    top: 0;
    z-index: 100;
}

.nav-brand {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.logo-icon {
    font-size: 1.5rem;
}

.nav-controls {
    display: flex;
    align-items: center;
    gap: 1.5rem;
}

.daily-goal {
    font-weight: 600;
    background: linear-gradient(135deg, var(--accent-primary), var(--accent-secondary));
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
}

/* Cards */
.glass-card {
    background: var(--glass-bg);
    backdrop-filter: blur(10px);
    border-radius: 16px;
    padding: 1.5rem;
    border: 1px solid var(--glass-border);
    box-shadow: var(--shadow);
    margin-bottom: 1.5rem;
}

/* Buttons */
.primary-btn {
    background: linear-gradient(135deg, var(--accent-primary), var(--accent-secondary));
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
    transition: var(--transition);
}

.primary-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.primary-btn:active {
    transform: translateY(0);
}

.icon-btn {
    background: none;
    border: none;
    color: var(--text-primary);
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 50%;
    display: grid;
    place-items: center;
    transition: var(--transition);
}

.icon-btn:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

/* Charts */
.calorie-summary {
    position: relative;
}

.chart-center {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
}

.chart-center span {
    font-size: 2rem;
    font-weight: 600;
    background: linear-gradient(135deg, var(--accent-primary), var(--accent-secondary));
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
}

/* Meal list */
.meal-list {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.meal-item {
    background-color: var(--bg-secondary);
    border-radius: 12px;
    padding: 1rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.meal-info {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.meal-name {
    font-weight: 500;
}

.meal-calories {
    color: var(--text-secondary);
    font-size: 0.875rem;
}

.meal-actions {
    display: flex;
    gap: 0.5rem;
}

/* Modal */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(5px);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    animation: fadeIn 0.3s ease-out;
}

.modal-content {
    background-color: var(--bg-secondary);
    border-radius: 16px;
    width: 90%;
    max-width: 500px;
    padding: 1.5rem;
    box-shadow: var(--shadow);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

/* Form styles */
.form-group {
    margin-bottom: 1.25rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-size: 0.875rem;
    color: var(--text-secondary);
}

.form-group input,
.form-group select {
    width: 100%;
    padding: 0.75rem 1rem;
    border-radius: 8px;
    border: 1px solid var(--glass-border);
    background-color: var(--bg-primary);
    color: var(--text-primary);
    font-family: inherit;
    transition: var(--transition);
}

.form-group input:focus,
.form-group select:focus {
    outline: none;
    border-color: var(--accent-primary);
    box-shadow: 0 0 0 2px rgba(123, 44, 191, 0.2);
}

/* Autocomplete */
.autocomplete {
    position: relative;
}

.autocomplete-results {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    max-height: 200px;
    overflow-y: auto;
    background-color: var(--bg-secondary);
    border-radius: 0 0 8px 8px;
    box-shadow: var(--shadow);
    z-index: 10;
    display: none;
}

.autocomplete-item {
    padding: 0.75rem 1rem;
    cursor: pointer;
    transition: var(--transition);
}

.autocomplete-item:hover {
    background-color: rgba(255, 255, 255, 0.05);
}

/* Mobile nav */
.mobile-nav {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    display: flex;
    justify-content: space-around;
    background-color: var(--bg-secondary);
    padding: 0.75rem 0;
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.2);
    z-index: 100;
    display: none;
}

.mobile-nav-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.25rem;
    background: none;
    border: none;
    color: var(--text-secondary);
    font-size: 0.75rem;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 8px;
    transition: var(--transition);
}

.mobile-nav-btn.active {
    color: var(--accent-primary);
}

.mobile-nav-btn i {
    font-size: 1.25rem;
}

/* Responsive styles */
@media (max-width: 768px) {
    .main-nav {
        padding: 1rem;
    }
    
    .mobile-nav {
        display: flex;
    }
    
    .app-container {
        padding-bottom: 120px;
    }
}

@media (max-width: 480px) {
    .section-title {
        font-size: 1.25rem;
    }
    
    .primary-btn {
        padding: 0.75rem 1rem;
    }
}
