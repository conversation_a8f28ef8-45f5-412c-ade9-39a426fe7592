<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dino Runner Challenge</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            background: linear-gradient(135deg, 
                #1a1a2e 0%, 
                #16213e 15%,
                #0f3460 30%,
                #533483 45%,
                #e94560 60%,
                #f38f5f 75%,
                #0f3460 90%,
                #16213e 100%
            );
            background-size: 400% 400%;
            animation: movingBackground 8s ease infinite;
            font-family: 'Courier New', monospace;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            overflow: hidden;
        }

        @keyframes movingBackground {
            0% { background-position: 0% 50%; }
            25% { background-position: 100% 50%; }
            50% { background-position: 100% 100%; }
            75% { background-position: 0% 100%; }
            100% { background-position: 0% 50%; }
        }

        .game-container {
            width: 900px;
            height: 400px;
            background: linear-gradient(to bottom, #87CEEB 0%, #DEB887 80%, #8FBC8F 100%);
            border: 4px solid #654321;
            border-radius: 15px;
            position: relative;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }

        .ground {
            position: absolute;
            bottom: 0;
            width: 200%;
            height: 80px;
            background: repeating-linear-gradient(
                90deg,
                #8FBC8F 0px,
                #8FBC8F 4px,
                #9ACD32 4px,
                #9ACD32 8px
            );
            animation: moveGround 2s linear infinite;
        }

        @keyframes moveGround {
            0% { transform: translateX(0); }
            100% { transform: translateX(-50%); }
        }

        .dino {
            position: absolute;
            bottom: 80px;
            left: 100px;
            width: 60px;
            height: 60px;
            background: #228B22;
            border-radius: 15px 15px 5px 5px;
            transition: all 0.2s ease;
            z-index: 10;
        }

        .dino::before {
            content: '';
            position: absolute;
            top: -15px;
            left: 15px;
            width: 30px;
            height: 30px;
            background: #228B22;
            border-radius: 50%;
        }

        .dino::after {
            content: '';
            position: absolute;
            top: -10px;
            left: 20px;
            width: 8px;
            height: 8px;
            background: #000;
            border-radius: 50%;
            box-shadow: 12px 0 0 #000;
        }

        .dino.jumping {
            animation: jump 0.6s ease;
        }

        .dino.ducking {
            height: 30px;
            bottom: 80px;
            border-radius: 25px;
        }

        .dino.ducking::before {
            top: -5px;
            left: 5px;
        }

        .dino.ducking::after {
            top: 0px;
            left: 10px;
        }

        @keyframes jump {
            0%, 100% { bottom: 80px; }
            50% { bottom: 200px; }
        }

        .obstacle {
            position: absolute;
            bottom: 80px;
            right: -60px;
            background: #8B4513;
            border-radius: 5px;
            animation: moveObstacle 3s linear infinite;
        }

        .cactus {
            width: 30px;
            height: 60px;
            background: #228B22;
            border-radius: 15px 15px 5px 5px;
        }

        .cactus::before {
            content: '';
            position: absolute;
            top: 20px;
            left: -10px;
            width: 15px;
            height: 20px;
            background: #228B22;
            border-radius: 10px;
        }

        .cactus::after {
            content: '';
            position: absolute;
            top: 20px;
            right: -10px;
            width: 15px;
            height: 20px;
            background: #228B22;
            border-radius: 10px;
        }

        .bird {
            width: 40px;
            height: 25px;
            background: #4B0082;
            border-radius: 50%;
            bottom: 240px;
            animation: moveObstacle 2.5s linear infinite, flap 0.3s ease-in-out infinite;
        }

        .bird::before {
            content: '';
            position: absolute;
            top: -5px;
            left: 25px;
            width: 15px;
            height: 15px;
            background: #4B0082;
            border-radius: 50% 0;
            transform: rotate(45deg);
        }

        @keyframes flap {
            0%, 100% { transform: translateY(0); }
            50% { transform: translateY(-10px); }
        }

        .power-up {
            position: absolute;
            bottom: 140px;
            right: -40px;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            animation: moveObstacle 3s linear infinite, pulse 1s ease-in-out infinite;
            z-index: 5;
        }

        .speed-boost {
            background: radial-gradient(circle, #FFD700, #FFA500);
            box-shadow: 0 0 15px #FFD700;
        }

        .invincible {
            background: radial-gradient(circle, #FF69B4, #FF1493);
            box-shadow: 0 0 15px #FF69B4;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.2); }
        }

        @keyframes moveObstacle {
            0% { right: -60px; }
            100% { right: 960px; }
        }

        .cloud {
            position: absolute;
            background: rgba(255, 255, 255, 0.8);
            border-radius: 50px;
            animation: moveClouds 15s linear infinite;
        }

        .cloud:nth-child(1) {
            top: 50px;
            left: 200px;
            width: 80px;
            height: 40px;
        }

        .cloud:nth-child(2) {
            top: 80px;
            left: 500px;
            width: 60px;
            height: 30px;
            animation-delay: -5s;
        }

        .cloud:nth-child(3) {
            top: 30px;
            left: 800px;
            width: 100px;
            height: 50px;
            animation-delay: -10s;
        }

        @keyframes moveClouds {
            0% { transform: translateX(0); }
            100% { transform: translateX(-1000px); }
        }

        .ui {
            position: absolute;
            top: 20px;
            left: 20px;
            color: #333;
            font-size: 18px;
            font-weight: bold;
            z-index: 20;
        }

        .game-over {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0, 0, 0, 0.9);
            color: white;
            padding: 30px;
            border-radius: 15px;
            text-align: center;
            display: none;
            z-index: 30;
        }

        .game-over h2 {
            font-size: 36px;
            margin-bottom: 20px;
            color: #FF6B6B;
        }

        .game-over button {
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
            border: none;
            padding: 15px 30px;
            font-size: 18px;
            border-radius: 8px;
            cursor: pointer;
            margin: 10px;
            transition: all 0.3s ease;
        }

        .game-over button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.3);
        }

        .start-screen {
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            transform: none;
            background: linear-gradient(135deg, 
                rgba(20, 20, 30, 0.85) 0%, 
                rgba(40, 20, 60, 0.85) 25%,
                rgba(60, 30, 20, 0.85) 50%,
                rgba(20, 40, 60, 0.85) 75%,
                rgba(30, 20, 40, 0.85) 100%
            );
            background-size: 300% 300%;
            animation: startScreenGlow 4s ease-in-out infinite, overlayMove 6s ease-in-out infinite;
            backdrop-filter: blur(15px);
            color: white;
            padding: 0;
            border-radius: 0;
            text-align: center;
            z-index: 30;
            border: none;
            max-width: none;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            overflow: hidden;
        }

        @keyframes overlayMove {
            0%, 100% { background-position: 0% 50%; }
            33% { background-position: 100% 25%; }
            66% { background-position: 50% 100%; }
        }

        @keyframes startScreenGlow {
            0%, 100% { 
                box-shadow: 
                    inset 0 0 100px rgba(255,100,50,0.1),
                    inset 0 0 200px rgba(100,150,255,0.1); 
            }
            50% { 
                box-shadow: 
                    inset 0 0 150px rgba(100,150,255,0.2),
                    inset 0 0 300px rgba(255,100,50,0.15); 
            }
        }

        .title-container {
            position: relative;
            margin-bottom: 30px;
        }

        .main-title {
            font-size: 72px;
            margin-bottom: 15px;
            background: linear-gradient(45deg, 
                #FF6B35, #F7931E, #FFD23F, #FF6B35, #FF1744, #E91E63, #9C27B0, #3F51B5
            );
            background-size: 400% 400%;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            animation: titleShine 4s ease-in-out infinite;
            text-shadow: 0 0 50px rgba(255,107,53,0.6);
            font-weight: bold;
            letter-spacing: 4px;
            text-transform: uppercase;
        }

        @keyframes titleShine {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }

        .subtitle {
            font-size: 24px;
            color: #FFD700;
            font-style: italic;
            margin-bottom: 30px;
            opacity: 0.9;
            text-shadow: 0 0 20px rgba(255,215,0,0.5);
        }

        .smoke-effect {
            position: absolute;
            width: 40px;
            height: 40px;
            background: radial-gradient(circle, rgba(255,255,255,0.3), transparent);
            border-radius: 50%;
            animation: smoke 4s ease-in-out infinite;
        }

        .smoke-2 {
            left: 20%;
            top: -10px;
            animation-delay: -1s;
            width: 30px;
            height: 30px;
        }

        .smoke-3 {
            right: 20%;
            top: -15px;
            animation-delay: -2s;
            width: 35px;
            height: 35px;
        }

        @keyframes smoke {
            0%, 100% { transform: translateY(0) scale(1); opacity: 0.3; }
            50% { transform: translateY(-20px) scale(1.2); opacity: 0.1; }
        }

        .game-preview {
            display: flex;
            justify-content: center;
            align-items: center;
            margin: 20px 0;
            font-size: 32px;
            gap: 20px;
        }

        .preview-dino {
            animation: previewBounce 2s ease-in-out infinite;
        }

        .preview-obstacles span {
            display: inline-block;
            margin: 0 10px;
            animation: previewFloat 3s ease-in-out infinite;
        }

        .preview-obstacles span:nth-child(2) {
            animation-delay: -1s;
        }

        .preview-obstacles span:nth-child(3) {
            animation-delay: -2s;
        }

        @keyframes previewBounce {
            0%, 100% { transform: translateY(0); }
            50% { transform: translateY(-10px); }
        }

        @keyframes previewFloat {
            0%, 100% { transform: translateY(0) rotate(0deg); }
            33% { transform: translateY(-5px) rotate(5deg); }
            66% { transform: translateY(5px) rotate(-5deg); }
        }

        .controls {
            margin: 30px 0;
            font-size: 16px;
            line-height: 1.8;
        }

        .control-item {
            display: flex;
            justify-content: center;
            align-items: center;
            margin: 15px 0;
            gap: 10px;
        }

        .key {
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
            padding: 8px 12px;
            border-radius: 8px;
            font-weight: bold;
            font-size: 14px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.3);
            min-width: 50px;
            text-align: center;
        }

        .action {
            color: #E0E0E0;
        }

        .powerup-info {
            background: rgba(255,255,255,0.05);
            padding: 20px;
            border-radius: 15px;
            margin: 20px 0;
            border: 1px solid rgba(255,255,255,0.1);
        }

        .powerup-items {
            display: flex;
            flex-direction: column;
            gap: 10px;
            margin-top: 10px;
        }

        .powerup-item {
            background: rgba(0,0,0,0.2);
            padding: 10px;
            border-radius: 8px;
            font-size: 14px;
        }

        .start-button-container {
            position: relative;
            margin: 30px 0;
        }

        .start-button {
            position: relative;
            background: linear-gradient(45deg, #FF6B35, #F7931E, #FFD23F, #FF6B35);
            background-size: 300% 300%;
            color: white;
            border: none;
            padding: 25px 50px;
            font-size: 24px;
            font-weight: bold;
            border-radius: 50px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 3px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(255,107,53,0.5);
            animation: buttonPulse 3s ease-in-out infinite;
        }

        .start-button:hover {
            transform: translateY(-3px) scale(1.05);
            box-shadow: 0 12px 35px rgba(255,107,53,0.6);
        }

        .button-glow {
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            animation: buttonGlow 3s ease-in-out infinite;
        }

        @keyframes buttonPulse {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }

        @keyframes buttonGlow {
            0% { left: -100%; }
            100% { left: 100%; }
        }

        .flavor-text {
            font-size: 16px;
            color: #B0B0B0;
            font-style: italic;
            margin-top: 20px;
        }

        .dino.invincible {
            animation: invincibleGlow 0.3s ease-in-out infinite;
        }

        @keyframes invincibleGlow {
            0%, 100% { filter: brightness(1) hue-rotate(0deg); }
            50% { filter: brightness(1.5) hue-rotate(60deg); }
        }

        .speed-mode .ground {
            animation-duration: 1s;
        }

        .speed-mode .obstacle {
            animation-duration: 1.5s;
        }

        .combo-counter {
            position: absolute;
            top: 60px;
            right: 20px;
            color: #FF6B6B;
            font-size: 16px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="game-container" id="gameContainer">
        <div class="cloud"></div>
        <div class="cloud"></div>
        <div class="cloud"></div>
        <div class="ground"></div>
        <div class="dino" id="dino"></div>
        
        <div class="ui">
            <div>Score: <span id="score">0</span></div>
            <div>High Score: <span id="highScore">0</span></div>
            <div>Level: <span id="level">1</span></div>
        </div>
        
        <div class="combo-counter" id="comboCounter" style="display: none;">
            Combo: <span id="comboValue">0</span>
        </div>

        <div class="start-screen" id="startScreen">
            <div class="title-container">
                <h1 class="main-title">🔥 THE SMOKEY RUN 🔥</h1>
                <div class="subtitle">An Epic Prehistoric Adventure</div>
                <div class="smoke-effect"></div>
                <div class="smoke-effect smoke-2"></div>
                <div class="smoke-effect smoke-3"></div>
            </div>
            
            <div class="game-preview">
                <div class="preview-dino">🦕</div>
                <div class="preview-obstacles">
                    <span>🌵</span>
                    <span>🦅</span>
                    <span>⚡</span>
                </div>
            </div>
            
            <div class="controls">
                <div class="control-item">
                    <span class="key">SPACE</span> or <span class="key">↑</span> 
                    <span class="action">Jump over obstacles</span>
                </div>
                <div class="control-item">
                    <span class="key">↓</span> 
                    <span class="action">Duck under flying enemies</span>
                </div>
                <div class="powerup-info">
                    <p><strong>🌟 Collect Power-ups for Special Abilities!</strong></p>
                    <div class="powerup-items">
                        <span class="powerup-item">🟡 <strong>Speed Boost</strong> - Blazing fast movement</span>
                        <span class="powerup-item">🔴 <strong>Invincibility</strong> - Unstoppable force</span>
                    </div>
                </div>
            </div>
            
            <div class="start-button-container">
                <button class="start-button" onclick="startGame()">
                    <span class="button-text">START YOUR JOURNEY</span>
                    <div class="button-glow"></div>
                </button>
            </div>
            
            <div class="flavor-text">
                Survive the prehistoric wasteland and become the ultimate runner!
            </div>
        </div>

        <div class="game-over" id="gameOver">
            <h2>Game Over!</h2>
            <p>Final Score: <span id="finalScore">0</span></p>
            <p>Best Combo: <span id="bestCombo">0</span></p>
            <button onclick="restartGame()">Play Again</button>
        </div>
    </div>

    <script>
        let game = {
            isRunning: false,
            isPaused: false,
            score: 0,
            level: 1,
            speed: 1.5,
            obstacleSpawnRate: 0.008,
            powerUpSpawnRate: 0.002,
            combo: 0,
            bestCombo: 0,
            invincible: false,
            speedBoost: false,
            invincibleTime: 0,
            speedBoostTime: 0
        };

        const dino = document.getElementById('dino');
        const gameContainer = document.getElementById('gameContainer');
        const scoreElement = document.getElementById('score');
        const levelElement = document.getElementById('level');
        const highScoreElement = document.getElementById('highScore');
        const startScreen = document.getElementById('startScreen');
        const gameOverScreen = document.getElementById('gameOver');
        const comboCounter = document.getElementById('comboCounter');

        let obstacles = [];
        let powerUps = [];
        let keys = {};
        let gameLoop;
        let isJumping = false;
        let isDucking = false;

        // Load high score
        let highScore = parseInt(sessionStorage.getItem('dinoHighScore')) || 0;
        highScoreElement.textContent = highScore;

        // Event listeners
        document.addEventListener('keydown', (e) => {
            keys[e.code] = true;
            if (game.isRunning && (e.code === 'Space' || e.code === 'ArrowUp')) {
                e.preventDefault();
                jump();
            } else if (game.isRunning && e.code === 'ArrowDown') {
                e.preventDefault();
                duck();
            }
        });

        document.addEventListener('keyup', (e) => {
            keys[e.code] = false;
            if (e.code === 'ArrowDown') {
                stopDuck();
            }
        });

        function startGame() {
            startScreen.style.display = 'none';
            game.isRunning = true;
            game.score = 0;
            game.level = 1;
            game.speed = 1.5;
            game.combo = 0;
            obstacles = [];
            powerUps = [];
            resetDino();
            gameLoop = setInterval(updateGame, 16);
        }

        function jump() {
            if (!isJumping && !isDucking) {
                isJumping = true;
                dino.classList.add('jumping');
                setTimeout(() => {
                    isJumping = false;
                    dino.classList.remove('jumping');
                }, 600);
            }
        }

        function duck() {
            if (!isJumping && !isDucking) {
                isDucking = true;
                dino.classList.add('ducking');
            }
        }

        function stopDuck() {
            isDucking = false;
            dino.classList.remove('ducking');
        }

        function resetDino() {
            dino.classList.remove('jumping', 'ducking', 'invincible');
            gameContainer.classList.remove('speed-mode');
            isJumping = false;
            isDucking = false;
            game.invincible = false;
            game.speedBoost = false;
            game.invincibleTime = 0;
            game.speedBoostTime = 0;
        }

        function spawnObstacle() {
            if (Math.random() < game.obstacleSpawnRate) {
                const obstacle = document.createElement('div');
                obstacle.className = 'obstacle';
                
                // Prevent spawning obstacles too close together
                const lastObstacle = obstacles[obstacles.length - 1];
                if (lastObstacle) {
                    const lastRect = lastObstacle.getBoundingClientRect();
                    if (lastRect.right > gameContainer.offsetWidth - 200) {
                        return; // Skip spawning if last obstacle is too close
                    }
                }
                
                if (Math.random() < 0.35) {
                    // Spawn bird - positioned higher to avoid jump collision
                    obstacle.classList.add('bird');
                    obstacle.style.bottom = Math.random() < 0.5 ? '240px' : '280px';
                } else {
                    // Spawn cactus
                    obstacle.classList.add('cactus');
                    obstacle.style.bottom = '80px';
                }
                
                obstacle.style.animationDuration = `${3 / game.speed}s`;
                gameContainer.appendChild(obstacle);
                obstacles.push(obstacle);
            }
        }

        function spawnPowerUp() {
            if (Math.random() < game.powerUpSpawnRate) {
                const powerUp = document.createElement('div');
                powerUp.className = 'power-up';
                
                if (Math.random() < 0.75) {
                    powerUp.classList.add('speed-boost');
                    powerUp.setAttribute('data-type', 'speed');
                } else {
                    powerUp.classList.add('invincible');
                    powerUp.setAttribute('data-type', 'invincible');
                }
                
                powerUp.style.animationDuration = `${3 / game.speed}s`;
                gameContainer.appendChild(powerUp);
                powerUps.push(powerUp);
            }
        }

        function checkCollisions() {
            const dinoRect = dino.getBoundingClientRect();
            
            // Check obstacle collisions
            obstacles.forEach((obstacle, index) => {
                const obstacleRect = obstacle.getBoundingClientRect();
                
                if (dinoRect.left < obstacleRect.right &&
                    dinoRect.right > obstacleRect.left &&
                    dinoRect.top < obstacleRect.bottom &&
                    dinoRect.bottom > obstacleRect.top) {
                    
                    if (!game.invincible) {
                        endGame();
                    } else {
                        // Remove obstacle if invincible
                        obstacle.remove();
                        obstacles.splice(index, 1);
                        game.score += 50;
                        game.combo++;
                        updateCombo();
                    }
                }
            });

            // Check power-up collisions
            powerUps.forEach((powerUp, index) => {
                const powerUpRect = powerUp.getBoundingClientRect();
                
                if (dinoRect.left < powerUpRect.right &&
                    dinoRect.right > powerUpRect.left &&
                    dinoRect.top < powerUpRect.bottom &&
                    dinoRect.bottom > powerUpRect.top) {
                    
                    collectPowerUp(powerUp);
                    powerUp.remove();
                    powerUps.splice(index, 1);
                    game.score += 100;
                    game.combo++;
                    updateCombo();
                }
            });
        }

        function collectPowerUp(powerUp) {
            const type = powerUp.getAttribute('data-type');
            
            if (type === 'speed') {
                game.speedBoost = true;
                game.speedBoostTime = 300; // 5 seconds at 60fps
                gameContainer.classList.add('speed-mode');
            } else if (type === 'invincible') {
                game.invincible = true;
                game.invincibleTime = 360; // 6 seconds at 60fps
                dino.classList.add('invincible');
            }
        }

        function updatePowerUps() {
            if (game.invincibleTime > 0) {
                game.invincibleTime--;
                if (game.invincibleTime === 0) {
                    game.invincible = false;
                    dino.classList.remove('invincible');
                }
            }

            if (game.speedBoostTime > 0) {
                game.speedBoostTime--;
                if (game.speedBoostTime === 0) {
                    game.speedBoost = false;
                    gameContainer.classList.remove('speed-mode');
                }
            }
        }

        function updateCombo() {
            if (game.combo > 0) {
                comboCounter.style.display = 'block';
                document.getElementById('comboValue').textContent = game.combo;
                if (game.combo > game.bestCombo) {
                    game.bestCombo = game.combo;
                }
            }
        }

        function cleanupObstacles() {
            obstacles = obstacles.filter(obstacle => {
                const rect = obstacle.getBoundingClientRect();
                if (rect.right < 0) {
                    obstacle.remove();
                    game.score += 10;
                    return false;
                }
                return true;
            });

            powerUps = powerUps.filter(powerUp => {
                const rect = powerUp.getBoundingClientRect();
                if (rect.right < 0) {
                    powerUp.remove();
                    game.combo = 0; // Reset combo if power-up missed
                    return false;
                }
                return true;
            });
        }

        function updateGame() {
            if (!game.isRunning) return;

            game.score++;
            
            // Increase difficulty more gradually
            const newLevel = Math.floor(game.score / 800) + 1;
            if (newLevel > game.level) {
                game.level = newLevel;
                game.speed = Math.min(5, 1.5 + (game.level - 1) * 0.3);
                game.obstacleSpawnRate = Math.min(0.018, 0.008 + (game.level - 1) * 0.0015);
            }

            spawnObstacle();
            spawnPowerUp();
            checkCollisions();
            cleanupObstacles();
            updatePowerUps();

            // Update UI
            scoreElement.textContent = game.score;
            levelElement.textContent = game.level;
        }

        function endGame() {
            game.isRunning = false;
            clearInterval(gameLoop);
            
            // Update high score
            if (game.score > highScore) {
                highScore = game.score;
                sessionStorage.setItem('dinoHighScore', highScore);
                highScoreElement.textContent = highScore;
            }

            document.getElementById('finalScore').textContent = game.score;
            document.getElementById('bestCombo').textContent = game.bestCombo;
            gameOverScreen.style.display = 'block';
            comboCounter.style.display = 'none';
        }

        function restartGame() {
            gameOverScreen.style.display = 'none';
            
            // Clean up existing obstacles and power-ups
            obstacles.forEach(obstacle => obstacle.remove());
            powerUps.forEach(powerUp => powerUp.remove());
            
            resetDino();
            startGame();
        }

        // Touch controls for mobile
        gameContainer.addEventListener('touchstart', (e) => {
            e.preventDefault();
            if (game.isRunning) {
                jump();
            }
        });
    </script>
</body>
</html>