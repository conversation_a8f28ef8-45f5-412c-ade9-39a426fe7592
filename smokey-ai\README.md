# 🐾 Smokey AI v1 - Multi-User Intelligent Chat Assistant

A complete multi-user chat assistant with beautiful authentication system and persistent memory.

## ✨ Features

### 🎨 **Modern UI Design**
- **Glassmorphism effects** with backdrop blur
- **Gradient backgrounds** with floating animations
- **Tailwind CSS** for responsive design
- **Dark theme** with purple accent colors
- **Mobile-first** responsive design

### 🔐 **Authentication System**
- **Email/Password validation** with real-time feedback
- **Password visibility toggle** with eye icon
- **Remember me** functionality
- **Social login buttons** (Google, GitHub) - UI ready
- **Form validation** with error messages

### 💾 **User Management**
- **Frontend**: localStorage-based user storage
- **Backend**: Node.js + Express with bcrypt password hashing
- **JSON file storage** for user data
- **Automatic user creation** on first login
- **Session management** with remember me option

### 🚀 **Advanced Features**
- **Loading states** with spinner animations
- **Toast notifications** for user feedback
- **Auto-redirect** to main app after login
- **Debug console** commands for testing
- **Error handling** and validation

## 📁 File Structure

```
smokey-ai/
├── login.html          # Modern login page
├── login.js            # Frontend login logic
├── index.html          # Main Smokey AI app
├── app.js              # Main app logic with memory system
├── style.css           # Styles for both login and main app
├── server.js           # Node.js backend (optional)
├── package.json        # Node.js dependencies
├── users.json          # User data storage (auto-created)
└── README.md           # This file
```

## 🚀 Quick Start

### Option 1: Frontend Only (localStorage)

1. **Open the login page:**
   ```
   Open: smokey-ai/login.html
   ```

2. **Test the login:**
   - Enter any email (e.g., `<EMAIL>`)
   - Enter password (min 6 characters)
   - Click "LOG IN"
   - Will redirect to main Smokey AI app

### Option 2: Full Backend Setup

1. **Install Node.js dependencies:**
   ```bash
   cd smokey-ai
   npm install
   ```

2. **Start the server:**
   ```bash
   npm start
   ```

3. **Open in browser:**
   ```
   http://localhost:3000
   ```

## 🧪 Testing the Login System

### **Frontend Testing (localStorage)**

1. **Open browser console** (F12)
2. **Available debug commands:**
   ```javascript
   debugUsers()    // View all stored users
   clearUsers()    // Clear all users
   ```

3. **Test scenarios:**
   - **New user**: Enter new email → Creates account
   - **Existing user**: Use same email → Validates password
   - **Validation**: Try invalid email/short password
   - **Remember me**: Check box → Saves email for next visit

### **Backend Testing (Node.js)**

1. **API Endpoints:**
   ```
   POST /api/auth/login    # Login/Register
   GET  /api/users         # View all users (debug)
   DELETE /api/users       # Clear all users (debug)
   ```

2. **Test with curl:**
   ```bash
   # Create/Login user
   curl -X POST http://localhost:3000/api/auth/login \
     -H "Content-Type: application/json" \
     -d '{"email":"<EMAIL>","password":"password123"}'

   # View all users
   curl http://localhost:3000/api/users
   ```

## 🎯 User Data Format

The system stores users in this JSON format:

```json
{
  "users": [
    {
      "id": *************,
      "email": "<EMAIL>",
      "password": "hashed_password_here",
      "createdAt": "2023-12-20T10:30:00.000Z",
      "lastLogin": "2023-12-20T10:30:00.000Z"
    }
  ]
}
```

## 🔧 Customization

### **Colors & Styling**
Edit the Tailwind config in `login.html`:
```javascript
colors: {
  'primary': '#8b5cf6',        // Purple theme
  'primary-dark': '#7c3aed',   // Darker purple
  'dark-bg': '#0f172a',        // Background
  'dark-surface': '#1e293b',   // Cards/surfaces
  'dark-border': '#334155',    // Borders
}
```

### **Validation Rules**
Edit validation in `login.js`:
```javascript
// Email validation
const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

// Password validation (currently min 6 chars)
if (password.length < 6) {
    // Change minimum length here
}
```

### **Redirect URL**
Change redirect destination in `login.js`:
```javascript
// Current: redirects to index.html
window.location.href = 'index.html';

// Change to your desired page
window.location.href = 'dashboard.html';
```

## 🛡️ Security Notes

### **Frontend Version (localStorage)**
- ⚠️ **Not secure** - passwords stored in plain text
- ✅ **Good for**: Demos, prototypes, local testing
- ✅ **Data persists** across browser sessions

### **Backend Version (Node.js)**
- ✅ **Secure** - passwords hashed with bcrypt
- ✅ **File-based** storage (users.json)
- ✅ **Production ready** with proper error handling
- 🔒 **Passwords** are properly hashed and salted

## 🎨 Design Inspiration

The login page design is inspired by modern authentication interfaces like:
- **Huly** (reference image provided)
- **Linear** - Clean, minimal design
- **Vercel** - Glassmorphism effects
- **GitHub** - Professional form styling

## 🚀 Next Steps

1. **Add email verification** system
2. **Implement password reset** functionality
3. **Add OAuth providers** (Google, GitHub)
4. **Database integration** (MongoDB, PostgreSQL)
5. **JWT token authentication**
6. **Rate limiting** and security headers
7. **User profile management**

## 🐛 Troubleshooting

### **Common Issues:**

1. **"Cannot find module" error:**
   ```bash
   npm install  # Install dependencies
   ```

2. **Port already in use:**
   ```bash
   # Change port in server.js or kill existing process
   lsof -ti:3000 | xargs kill -9  # Kill process on port 3000
   ```

3. **Login not working:**
   - Check browser console for errors
   - Verify email format is valid
   - Ensure password is at least 6 characters

4. **Redirect not working:**
   - Make sure `index.html` exists in same folder
   - Check browser console for navigation errors

---

**🎉 Enjoy your modern Smokey AI login system!** 🐾
