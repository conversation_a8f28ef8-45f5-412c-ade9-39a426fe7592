document.addEventListener('DOMContentLoaded', function() {
    // DOM Elements
    const elements = {
        cityInput: document.getElementById('city-input'),
        addCityBtn: document.getElementById('add-city-btn'),
        weatherCards: document.getElementById('weather-cards'),
        forecastSection: document.getElementById('forecast-section'),
        forecastCards: document.getElementById('forecast-cards'),
        forecastCityName: document.getElementById('forecast-city-name'),
        themeToggle: document.getElementById('theme-toggle'),
        celsiusBtn: document.getElementById('celsius-btn'),
        fahrenheitBtn: document.getElementById('fahrenheit-btn'),
        refreshAllBtn: document.getElementById('refresh-all-btn'),
        loadingOverlay: document.getElementById('loading-overlay'),
        weatherAnimation: document.getElementById('weather-animation'),
        lastUpdatedTime: document.getElementById('last-updated-time')
    };

    // App State
    const state = {
        cities: JSON.parse(localStorage.getItem('weatherCities')) || [],
        unit: localStorage.getItem('weatherUnit') || 'metric',
        theme: localStorage.getItem('weatherTheme') || 'dark',
        currentForecastCity: null,
        isLoading: false
    };

    // API Configuration
    const config = {
        apiKey: 'YOUR_OPENWEATHERMAP_API_KEY', // Replace with your actual API key
        baseUrl: 'https://api.openweathermap.org/data/2.5',
        geoEndpoint: '/weather',
        onecallEndpoint: '/onecall',
        iconBaseUrl: 'https://openweathermap.org/img/wn/'
    };

    // Initialize the app
    init();

    // Event Listeners
    function setupEventListeners() {
        // City management
        elements.addCityBtn.addEventListener('click', handleAddCity);
        elements.cityInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') handleAddCity();
        });

        // UI controls
        elements.themeToggle.addEventListener('click', toggleTheme);
        elements.celsiusBtn.addEventListener('click', () => setUnit('metric'));
        elements.fahrenheitBtn.addEventListener('click', () => setUnit('imperial'));
        elements.refreshAllBtn.addEventListener('click', refreshAllCities);

        // Sample city buttons
        document.querySelectorAll('.sample-city').forEach(btn => {
            btn.addEventListener('click', () => {
                elements.cityInput.value = btn.dataset.city;
                handleAddCity();
            });
        });
    }

    // Initialization
    function init() {
        // Set initial theme and unit
        document.documentElement.setAttribute('data-theme', state.theme);
        updateThemeIcon();
        updateUnitButtons();

        // Load saved cities
        if (state.cities.length > 0) {
            loadAllCitiesWeather();
        }

        // Update last updated time
        updateLastUpdatedTime();

        // Setup event listeners
        setupEventListeners();
    }

    // City Management
    async function handleAddCity() {
        const cityName = elements.cityInput.value.trim();
        if (!cityName) return;

        if (state.cities.includes(cityName)) {
            showError(`${cityName} is already in your list`);
            return;
        }

        try {
            setLoading(true);
            const weatherData = await fetchCityWeather(cityName);
            
            if (weatherData) {
                state.cities.push(cityName);
                saveToLocalStorage('weatherCities', state.cities);
                renderWeatherCard(weatherData);
                elements.cityInput.value = '';
                checkNoCities();
            }
        } catch (error) {
            console.error(`Error adding city ${cityName}:`, error);
            showError(`Could not add ${cityName}: ${error.message}`);
        } finally {
            setLoading(false);
        }
    }

    function removeCity(cityName) {
        state.cities = state.cities.filter(city => city !== cityName);
        saveToLocalStorage('weatherCities', state.cities);

        const card = document.querySelector(`.weather-card[data-city="${cityName}"]`);
        if (card) {
            animateElement(card, 'fadeOut', () => {
                card.remove();
                checkNoCities();
                
                if (state.currentForecastCity === cityName) {
                    hideForecast();
                }
            });
        }
    }

    // Weather Data Fetching
    async function fetchCityWeather(cityName) {
        try {
            // First get city coordinates
            const geoData = await fetchData(
                `${config.baseUrl}${config.geoEndpoint}?q=${cityName}&appid=${config.apiKey}`
            );

            if (geoData.cod !== 200) {
                throw new Error(geoData.message || 'City not found');
            }

            // Then get weather data
            const weatherData = await fetchData(
                `${config.baseUrl}${config.onecallEndpoint}?lat=${geoData.coord.lat}&lon=${geoData.coord.lon}` +
                `&exclude=minutely,hourly,alerts&units=${state.unit}&appid=${config.apiKey}`
            );

            return {
                cityName,
                country: geoData.sys.country,
                current: weatherData.current,
                daily: weatherData.daily.slice(0, 3),
                timezone: weatherData.timezone
            };
        } catch (error) {
            // Handle specific API errors
            if (error.message.includes('not found') || error.message.includes('404')) {
                // Remove city if it's not found
                state.cities = state.cities.filter(city => city !== cityName);
                saveToLocalStorage('weatherCities', state.cities);
                throw new Error('City not found');
            }
            throw error;
        }
    }

    async function fetchData(url) {
        const response = await fetch(url);
        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.message || 'Failed to fetch data');
        }
        return await response.json();
    }

    // Rendering Functions
    function renderWeatherCard(weatherData) {
        const { cityName, country, current, daily } = weatherData;
        const existingCard = document.querySelector(`.weather-card[data-city="${cityName}"]`);

        const cardHtml = createWeatherCardHtml(cityName, country, current);
        
        if (existingCard) {
            existingCard.innerHTML = cardHtml;
        } else {
            const cardElement = document.createElement('div');
            cardElement.className = `weather-card animate__animated animate__fadeIn ${getWeatherClass(current.weather[0].main)}`;
            cardElement.dataset.city = cityName;
            cardElement.innerHTML = cardHtml;
            elements.weatherCards.appendChild(cardElement);
        }

        // Add event listeners to the card
        const card = document.querySelector(`.weather-card[data-city="${cityName}"]`);
        card.addEventListener('click', () => showForecast(cityName, country, daily));
        
        // Add delete button handler
        const deleteBtn = card.querySelector('.weather-card-delete');
        deleteBtn.addEventListener('click', (e) => {
            e.stopPropagation();
            removeCity(cityName);
        });

        // Update weather animations
        updateWeatherAnimations(current.weather[0].main);
    }

    function createWeatherCardHtml(cityName, country, currentWeather) {
        const weatherCondition = currentWeather.weather[0].main.toLowerCase();
        const weatherIcon = getWeatherIcon(currentWeather.weather[0].id, currentWeather.weather[0].icon);
        const temp = Math.round(currentWeather.temp);
        const feelsLike = Math.round(currentWeather.feels_like);
        const humidity = currentWeather.humidity;
        
        const windSpeed = state.unit === 'metric' 
            ? Math.round(currentWeather.wind_speed * 3.6) // Convert m/s to km/h
            : Math.round(currentWeather.wind_speed); // Already in mph
        
        const windUnit = state.unit === 'metric' ? 'km/h' : 'mph';

        return `
            <div class="weather-card-header">
                <div>
                    <div class="weather-card-city">${cityName}</div>
                    <div class="weather-card-country">${country}</div>
                </div>
                <button class="weather-card-delete" aria-label="Remove ${cityName}">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="weather-card-main">
                <div class="weather-card-temp">${temp}°${state.unit === 'metric' ? 'C' : 'F'}</div>
                <div>
                    <i class="wi ${weatherIcon} weather-card-icon"></i>
                    <div class="weather-card-desc">${capitalizeFirstLetter(currentWeather.weather[0].description)}</div>
                </div>
            </div>
            <div class="weather-card-details">
                <div class="weather-card-detail">
                    <i class="wi wi-humidity"></i>
                    <span class="weather-card-detail-label">Humidity</span>
                    <span class="weather-card-detail-value">${humidity}%</span>
                </div>
                <div class="weather-card-detail">
                    <i class="wi wi-strong-wind"></i>
                    <span class="weather-card-detail-label">Wind</span>
                    <span class="weather-card-detail-value">${windSpeed} ${windUnit}</span>
                </div>
                <div class="weather-card-detail">
                    <i class="wi wi-thermometer"></i>
                    <span class="weather-card-detail-label">Feels Like</span>
                    <span class="weather-card-detail-value">${feelsLike}°${state.unit === 'metric' ? 'C' : 'F'}</span>
                </div>
            </div>
        `;
    }

    function showForecast(cityName, country, dailyForecast) {
        state.currentForecastCity = cityName;
        
        // Update forecast header
        elements.forecastCityName.textContent = `${cityName}, ${country}`;
        
        // Clear previous forecast cards
        elements.forecastCards.innerHTML = '';
        
        // Create forecast cards
        dailyForecast.forEach(day => {
            const date = new Date(day.dt * 1000);
            const dayName = date.toLocaleDateString('en-US', { weekday: 'long' });
            const shortDate = date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
            
            const weatherIcon = getWeatherIcon(day.weather[0].id, day.weather[0].icon);
            const tempMax = Math.round(day.temp.max);
            const tempMin = Math.round(day.temp.min);
            
            const forecastCard = document.createElement('div');
            forecastCard.className = 'forecast-card animate__animated animate__fadeIn';
            forecastCard.innerHTML = `
                <div class="forecast-day">${dayName}</div>
                <div class="forecast-date">${shortDate}</div>
                <i class="wi ${weatherIcon} forecast-icon"></i>
                <div class="forecast-temp">
                    <span class="forecast-temp-high">${tempMax}°</span>
                    <span class="forecast-temp-low">${tempMin}°</span>
                </div>
            `;
            
            elements.forecastCards.appendChild(forecastCard);
        });
        
        // Show forecast section
        elements.forecastSection.style.display = 'block';
    }

    function hideForecast() {
        state.currentForecastCity = null;
        elements.forecastSection.style.display = 'none';
    }

    // UI Controls
    function toggleTheme() {
        state.theme = state.theme === 'dark' ? 'light' : 'dark';
        document.documentElement.setAttribute('data-theme', state.theme);
        saveToLocalStorage('weatherTheme', state.theme);
        updateThemeIcon();
    }

    function setUnit(unit) {
        if (state.unit === unit) return;
        
        state.unit = unit;
        saveToLocalStorage('weatherUnit', unit);
        updateUnitButtons();
        
        // Refresh all cities to get data in new units
        if (state.cities.length > 0) {
            refreshAllCities();
        }
    }

    // Helper Functions
    function updateThemeIcon() {
        const icon = elements.themeToggle.querySelector('i');
        icon.className = state.theme === 'light' ? 'fas fa-moon' : 'fas fa-sun';
    }

    function updateUnitButtons() {
        elements.celsiusBtn.classList.toggle('active', state.unit === 'metric');
        elements.fahrenheitBtn.classList.toggle('active', state.unit === 'imperial');
    }

    function updateWeatherAnimations(weatherCondition) {
        // Clear previous animations
        elements.weatherAnimation.innerHTML = '';
        
        // Add new animations based on weather condition
        switch (weatherCondition.toLowerCase()) {
            case 'clear':
                createSunAnimation();
                break;
            case 'clouds':
                createCloudAnimation(3);
                break;
            case 'rain':
                createRainAnimation(50);
                break;
            case 'snow':
                createSnowAnimation(30);
                break;
            case 'thunderstorm':
                createRainAnimation(40);
                createLightningAnimation();
                break;
            case 'drizzle':
                createRainAnimation(20);
                break;
            default:
                // No special animation for other conditions
                break;
        }
    }

    function createSunAnimation() {
        const sun = document.createElement('div');
        sun.className = 'sun';
        elements.weatherAnimation.appendChild(sun);
    }

    function createCloudAnimation(count) {
        for (let i = 0; i < count; i++) {
            const cloud = document.createElement('div');
            cloud.className = 'cloud';
            
            // Random positioning and size
            const size = Math.random() * 100 + 50;
            const left = Math.random() * 100;
            const top = Math.random() * 30;
            const opacity = Math.random() * 0.5 + 0.3;
            const duration = Math.random() * 40 + 60;
            
            cloud.style.width = `${size}px`;
            cloud.style.height = `${size * 0.6}px`;
            cloud.style.left = `${left}%`;
            cloud.style.top = `${top}%`;
            cloud.style.opacity = opacity;
            cloud.style.animation = `float ${duration}s infinite alternate ease-in-out`;
            
            elements.weatherAnimation.appendChild(cloud);
        }
    }

    function createRainAnimation(dropCount) {
        for (let i = 0; i < dropCount; i++) {
            const rain = document.createElement('div');
            rain.className = 'rain';
            
            // Random positioning and animation duration
            const left = Math.random() * 100;
            const duration = Math.random() * 1 + 0.5;
            const delay = Math.random() * 2;
            const length = Math.random() * 20 + 10;
            
            rain.style.left = `${left}%`;
            rain.style.animationDuration = `${duration}s`;
            rain.style.animationDelay = `${delay}s`;
            rain.style.height = `${length}px`;
            
            elements.weatherAnimation.appendChild(rain);
        }
    }

    function createSnowAnimation(flakeCount) {
        for (let i = 0; i < flakeCount; i++) {
            const snow = document.createElement('div');
            snow.className = 'snow';
            
            // Random positioning and animation
            const left = Math.random() * 100;
            const size = Math.random() * 8 + 4;
            const duration = Math.random() * 10 + 5;
            const delay = Math.random() * 5;
            
            snow.style.left = `${left}%`;
            snow.style.width = `${size}px`;
            snow.style.height = `${size}px`;
            snow.style.animationDuration = `${duration}s`;
            snow.style.animationDelay = `${delay}s`;
            
            elements.weatherAnimation.appendChild(snow);
        }
    }

    function createLightningAnimation() {
        // This would be more complex, but for simplicity:
        const lightning = document.createElement('div');
        lightning.className = 'lightning';
        lightning.style.position = 'absolute';
        lightning.style.width = '100%';
        lightning.style.height = '100%';
        lightning.style.background = 'rgba(255, 255, 255, 0.7)';
        lightning.style.animation = 'flash 0.5s infinite alternate';
        
        elements.weatherAnimation.appendChild(lightning);
        
        // Remove after a few flashes
        setTimeout(() => {
            lightning.remove();
        }, 3000);
    }

    async function refreshAllCities() {
        if (state.cities.length === 0 || state.isLoading) return;
        
        try {
            setLoading(true);
            elements.refreshAllBtn.disabled = true;
            elements.refreshAllBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Refreshing...';
            
            const promises = state.cities.map(city => fetchCityWeather(city));
            const results = await Promise.all(promises);
            
            // Update all weather cards
            results.forEach(data => {
                if (data) renderWeatherCard(data);
            });
            
            updateLastUpdatedTime();
        } catch (error) {
            console.error('Error refreshing cities:', error);
            showError('Failed to refresh some cities. Please try again.');
        } finally {
            setLoading(false);
            elements.refreshAllBtn.disabled = false;
            elements.refreshAllBtn.innerHTML = '<i class="fas fa-sync-alt"></i> Refresh All';
        }
    }

    function loadAllCitiesWeather() {
        if (state.cities.length === 0) return;
        
        setLoading(true);
        
        Promise.all(state.cities.map(city => fetchCityWeather(city)))
            .then(results => {
                // Hide welcome card
                const welcomeCard = document.querySelector('.welcome-card');
                if (welcomeCard) {
                    animateElement(welcomeCard, 'fadeOut', () => welcomeCard.remove());
                }
                
                // Render all weather cards
                results.forEach(data => {
                    if (data) renderWeatherCard(data);
                });
            })
            .catch(error => {
                console.error('Error loading cities:', error);
                showError('Failed to load some cities. Please try refreshing.');
            })
            .finally(() => {
                setLoading(false);
            });
    }

    // Utility Functions
    function setLoading(isLoading) {
        state.isLoading = isLoading;
        if (isLoading) {
            elements.loadingOverlay.classList.add('active');
        } else {
            elements.loadingOverlay.classList.remove('active');
        }
    }

    function showError(message) {
        const errorElement = document.createElement('div');
        errorElement.className = 'error-message animate__animated animate__fadeIn';
        errorElement.textContent = message;
        
        document.body.appendChild(errorElement);
        
        setTimeout(() => {
            animateElement(errorElement, 'fadeOut', () => {
                errorElement.remove();
            });
        }, 3000);
    }

    function animateElement(element, animation, callback) {
        element.classList.add('animate__animated', `animate__${animation}`);
        
        const handleAnimationEnd = () => {
            element.classList.remove('animate__animated', `animate__${animation}`);
            element.removeEventListener('animationend', handleAnimationEnd);
            if (callback) callback();
        };
        
        element.addEventListener('animationend', handleAnimationEnd);
    }

    function checkNoCities() {
        if (state.cities.length === 0 && !document.querySelector('.welcome-card')) {
            const welcomeCard = document.createElement('div');
            welcomeCard.className = 'welcome-card animate__animated animate__fadeIn';
            welcomeCard.innerHTML = `
                <div class="welcome-icon">
                    <i class="wi wi-day-sunny"></i>
                </div>
                <h3>Welcome to WeatherSphere!</h3>
                <p>Add cities to see their current weather conditions and forecasts</p>
                <div class="sample-cities">
                    <button class="sample-city" data-city="New York">New York</button>
                    <button class="sample-city" data-city="London">London</button>
                    <button class="sample-city" data-city="Tokyo">Tokyo</button>
                </div>
            `;
            
            elements.weatherCards.appendChild(welcomeCard);
            
            // Add event listeners to sample city buttons
            welcomeCard.querySelectorAll('.sample-city').forEach(btn => {
                btn.addEventListener('click', () => {
                    elements.cityInput.value = btn.dataset.city;
                    handleAddCity();
                });
            });
        }
    }

    function updateLastUpdatedTime() {
        const now = new Date();
        const timeString = now.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
        const dateString = now.toLocaleDateString([], { weekday: 'short', month: 'short', day: 'numeric' });
        elements.lastUpdatedTime.textContent = `${dateString} at ${timeString}`;
    }

    function saveToLocalStorage(key, value) {
        localStorage.setItem(key, JSON.stringify(value));
    }

    function getWeatherIcon(weatherId, iconCode) {
        // Map weather conditions to Weather Icons classes
        if (weatherId >= 200 && weatherId < 300) {
            return 'wi-thunderstorm';
        } else if (weatherId >= 300 && weatherId < 400) {
            return 'wi-sprinkle';
        } else if (weatherId >= 500 && weatherId < 600) {
            return iconCode.includes('d') ? 'wi-day-rain' : 'wi-night-alt-rain';
        } else if (weatherId >= 600 && weatherId < 700) {
            return iconCode.includes('d') ? 'wi-day-snow' : 'wi-night-alt-snow';
        } else if (weatherId >= 700 && weatherId < 800) {
            return 'wi-fog';
        } else if (weatherId === 800) {
            return iconCode.includes('d') ? 'wi-day-sunny' : 'wi-night-clear';
        } else if (weatherId === 801) {
            return iconCode.includes('d') ? 'wi-day-sunny-overcast' : 'wi-night-alt-partly-cloudy';
        } else if (weatherId > 801 && weatherId < 900) {
            return iconCode.includes('d') ? 'wi-day-cloudy' : 'wi-night-alt-cloudy';
        } else {
            return 'wi-cloud';
        }
    }

    function getWeatherClass(weatherCondition) {
        switch (weatherCondition.toLowerCase()) {
            case 'clear': return 'sunny';
            case 'clouds': return 'cloudy';
            case 'rain': return 'rainy';
            case 'snow': return 'snowy';
            case 'thunderstorm': return 'stormy';
            case 'drizzle': return 'rainy';
            case 'mist':
            case 'smoke':
            case 'haze':
            case 'fog':
            case 'sand':
            case 'dust':
            case 'ash':
            case 'squall':
            case 'tornado':
                return 'foggy';
            default: return '';
        }
    }

    function capitalizeFirstLetter(string) {
        return string.charAt(0).toUpperCase() + string.slice(1);
    }
});