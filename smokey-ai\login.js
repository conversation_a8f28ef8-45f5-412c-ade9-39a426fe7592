class LoginManager {
    constructor() {
        this.users = this.loadUsers();
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.setupPasswordToggle();
    }

    setupEventListeners() {
        const form = document.getElementById('loginForm');
        const emailInput = document.getElementById('email');
        const passwordInput = document.getElementById('password');

        // Form submission
        form.addEventListener('submit', (e) => this.handleSubmit(e));

        // Real-time validation
        emailInput.addEventListener('blur', () => this.validateEmail());
        emailInput.addEventListener('input', () => this.clearError('email'));

        passwordInput.addEventListener('blur', () => this.validatePassword());
        passwordInput.addEventListener('input', () => this.clearError('password'));

        // Social login buttons
        document.querySelectorAll('button[type="button"]').forEach(btn => {
            if (btn.textContent.includes('Google') || btn.textContent.includes('GitHub')) {
                btn.addEventListener('click', (e) => this.handleSocialLogin(e));
            }
        });
    }

    setupPasswordToggle() {
        const toggleBtn = document.getElementById('togglePassword');
        const passwordInput = document.getElementById('password');
        const eyeIcon = document.getElementById('eyeIcon');

        toggleBtn.addEventListener('click', () => {
            const isPassword = passwordInput.type === 'password';
            passwordInput.type = isPassword ? 'text' : 'password';

            // Update icon
            eyeIcon.innerHTML = isPassword
                ? `<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21"></path>`
                : `<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>`;
        });
    }

    async handleSubmit(e) {
        e.preventDefault();

        const emailOrUsername = document.getElementById('email').value.trim();
        const password = document.getElementById('password').value;
        const remember = document.getElementById('remember').checked;

        // Clear previous errors
        this.clearAllErrors();

        // Validate inputs
        const isEmailValid = this.validateEmailOrUsername();
        const isPasswordValid = this.validatePassword();

        if (!isEmailValid || !isPasswordValid) {
            this.showToast('Please fix the errors above', 'error');
            return;
        }

        // Show loading state
        this.setLoadingState(true);

        try {
            // Simulate API delay
            await this.delay(1500);

            // Check if user exists (by email or username)
            console.log('Looking for user:', emailOrUsername);
            console.log('Available users:', this.users);

            const existingUser = this.findUserByEmailOrUsername(emailOrUsername);
            console.log('Found user:', existingUser);

            if (existingUser) {
                // Check password
                if (existingUser.password === password) {
                    console.log('Password correct, logging in...');
                    this.handleSuccessfulLogin(existingUser, remember);
                } else {
                    console.log('Password incorrect');
                    this.showError('password', 'Incorrect password');
                    this.showToast('Invalid credentials', 'error');
                }
            } else {
                console.log('User not found');
                this.showError('email', 'User not found. Please register first.');
                this.showToast('User not found. Please register first.', 'error');
            }

        } catch (error) {
            console.error('Login error:', error);
            this.showToast('Something went wrong. Please try again.', 'error');
        } finally {
            this.setLoadingState(false);
        }
    }

    validateEmailOrUsername() {
        const input = document.getElementById('email').value.trim();
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        const usernameRegex = /^[a-zA-Z0-9_]+$/;

        if (!input) {
            this.showError('email', 'Email or username is required');
            return false;
        }

        // Check if it's an email or username
        const isEmail = emailRegex.test(input);
        const isUsername = usernameRegex.test(input) && input.length >= 3 && input.length <= 20;

        if (!isEmail && !isUsername) {
            this.showError('email', 'Please enter a valid email address or username');
            return false;
        }

        return true;
    }

    validatePassword() {
        const password = document.getElementById('password').value;

        if (!password) {
            this.showError('password', 'Password is required');
            return false;
        }

        if (password.length < 6) {
            this.showError('password', 'Password must be at least 6 characters long');
            return false;
        }

        return true;
    }

    showError(field, message) {
        const errorElement = document.getElementById(`${field}Error`);
        const inputElement = document.getElementById(field);

        errorElement.textContent = message;
        errorElement.classList.remove('hidden');
        inputElement.classList.add('border-red-500');
    }

    clearError(field) {
        const errorElement = document.getElementById(`${field}Error`);
        const inputElement = document.getElementById(field);

        errorElement.classList.add('hidden');
        inputElement.classList.remove('border-red-500');
    }

    clearAllErrors() {
        ['email', 'password'].forEach(field => this.clearError(field));
    }

    setLoadingState(loading) {
        const loginBtn = document.getElementById('loginBtn');
        const loginText = document.getElementById('loginText');
        const loginSpinner = document.getElementById('loginSpinner');

        if (loading) {
            loginBtn.disabled = true;
            loginBtn.classList.add('opacity-75', 'cursor-not-allowed');
            loginText.classList.add('hidden');
            loginSpinner.classList.remove('hidden');
        } else {
            loginBtn.disabled = false;
            loginBtn.classList.remove('opacity-75', 'cursor-not-allowed');
            loginText.classList.remove('hidden');
            loginSpinner.classList.add('hidden');
        }
    }

    loadUsers() {
        try {
            const usersData = localStorage.getItem('users');
            if (!usersData) {
                return { users: [] };
            }

            const parsed = JSON.parse(usersData);

            // Ensure the structure is valid
            if (!parsed || !Array.isArray(parsed.users)) {
                console.warn('Invalid users data structure in login, resetting...');
                return { users: [] };
            }

            // Filter out any invalid user objects
            const validUsers = parsed.users.filter(user => {
                if (!user || typeof user !== 'object') {
                    console.warn('Removing invalid user object in login:', user);
                    return false;
                }
                if (!user.username || !user.email) {
                    console.warn('Removing user with missing username/email in login:', user);
                    return false;
                }
                return true;
            });

            // If we filtered out any users, save the cleaned data
            if (validUsers.length !== parsed.users.length) {
                console.log(`Cleaned users data in login: ${parsed.users.length} -> ${validUsers.length}`);
                const cleanedData = { users: validUsers };
                localStorage.setItem('users', JSON.stringify(cleanedData));
                return cleanedData;
            }

            return parsed;
        } catch (error) {
            console.error('Error loading users in login:', error);
            // Reset to empty if there's a parsing error
            localStorage.removeItem('users');
            return { users: [] };
        }
    }

    saveUsers() {
        try {
            localStorage.setItem('users', JSON.stringify(this.users));
            console.log('Users saved successfully:', this.users);
        } catch (error) {
            console.error('Error saving users:', error);
        }
    }

    findUserByEmailOrUsername(input) {
        const inputLower = input.toLowerCase();
        return this.users.users.find(user => {
            // Check if user object is valid
            if (!user || typeof user !== 'object') {
                console.warn('Invalid user object found:', user);
                return false;
            }

            // Check if user has required properties
            if (!user.email || !user.username) {
                console.warn('User missing email/username:', user);
                return false;
            }

            try {
                return user.email.toLowerCase() === inputLower ||
                       user.username.toLowerCase() === inputLower;
            } catch (error) {
                console.warn('Error comparing user data:', error, user);
                return false;
            }
        });
    }

    handleSuccessfulLogin(user, remember) {
        // Update last login
        user.lastLogin = new Date().toISOString();
        this.saveUsers();

        // Update user's individual data file
        const userData = this.loadUserData(user.username);
        if (userData) {
            userData.lastLogin = new Date().toISOString();
            this.saveUserData(user.username, userData);
        }

        // Store session with username
        const sessionData = {
            username: user.username,
            email: user.email,
            loginTime: new Date().toISOString()
        };

        if (remember) {
            localStorage.setItem('rememberedUser', user.username);
            localStorage.setItem('currentSession', JSON.stringify(sessionData));
        } else {
            sessionStorage.setItem('currentSession', JSON.stringify(sessionData));
        }

        this.showToast('Login successful! Redirecting...', 'success');

        // Redirect to main Smokey AI app
        setTimeout(() => {
            window.location.href = 'index.html';
        }, 2000);
    }

    loadUserData(username) {
        try {
            const userData = localStorage.getItem(`user_${username.toLowerCase()}`);
            return userData ? JSON.parse(userData) : null;
        } catch (error) {
            console.error('Error loading user data:', error);
            return null;
        }
    }

    saveUserData(username, userData) {
        try {
            localStorage.setItem(`user_${username.toLowerCase()}`, JSON.stringify(userData));
        } catch (error) {
            console.error('Error saving user data:', error);
        }
    }

    handleSocialLogin(e) {
        const provider = e.target.textContent.includes('Google') ? 'Google' : 'GitHub';
        this.showToast(`${provider} login coming soon! 🚀`, 'info');
    }

    showToast(message, type = 'success') {
        const toast = document.getElementById('toast');
        const toastMessage = document.getElementById('toastMessage');

        // Update message
        toastMessage.textContent = message;

        // Update colors based on type
        const toastDiv = toast.querySelector('div');
        toastDiv.className = `px-6 py-3 rounded-lg shadow-lg flex items-center ${
            type === 'success' ? 'bg-green-500 text-white' :
            type === 'error' ? 'bg-red-500 text-white' :
            type === 'info' ? 'bg-blue-500 text-white' :
            'bg-gray-500 text-white'
        }`;

        // Show toast
        toast.classList.remove('hidden');

        // Auto hide after 4 seconds
        setTimeout(() => {
            toast.classList.add('hidden');
        }, 4000);
    }

    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    // Debug method to view all users
    getAllUsers() {
        console.log('All users:', this.users);
        return this.users;
    }

    // Debug method to clear all users
    clearAllUsers() {
        this.users = { users: [] };
        this.saveUsers();
        console.log('All users cleared');
    }
}

// Initialize the login manager when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    // Clear any corrupted data first
    try {
        const usersData = localStorage.getItem('users');
        if (usersData) {
            const parsed = JSON.parse(usersData);
            console.log('Current users data in login:', parsed);
        }
    } catch (error) {
        console.warn('Corrupted users data found in login, clearing...', error);
        localStorage.removeItem('users');
    }

    window.loginManager = new LoginManager();

    // Auto-fill remembered user
    const rememberedUser = localStorage.getItem('rememberedUser');
    if (rememberedUser) {
        document.getElementById('email').value = rememberedUser;
        document.getElementById('remember').checked = true;
    }

    // Enhanced debug functions
    window.debugLogin = {
        viewUsers: () => {
            const users = localStorage.getItem('users');
            console.log('👥 Users data:', users ? JSON.parse(users) : 'No users found');
            return users ? JSON.parse(users) : null;
        },
        clearAllData: () => {
            localStorage.removeItem('users');
            for (let i = localStorage.length - 1; i >= 0; i--) {
                const key = localStorage.key(i);
                if (key && key.startsWith('user_')) {
                    localStorage.removeItem(key);
                }
            }
            console.log('🗑️ All login data cleared');
        },
        testLogin: (username, password) => {
            document.getElementById('email').value = username;
            document.getElementById('password').value = password;
            console.log(`🧪 Test login set for: ${username}`);
        }
    };

    console.log('🚀 Login Manager initialized!');
    console.log('🧹 Data validation complete!');
    console.log('🛠️ Debug commands available:');
    console.log('  debugLogin.viewUsers() - View all users');
    console.log('  debugLogin.clearAllData() - Clear all data');
    console.log('  debugLogin.testLogin("username", "password") - Fill login form');
});
