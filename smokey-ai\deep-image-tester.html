<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Deep Image API Tester - Smokey AI</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
    </style>
</head>
<body class="flex items-center justify-center p-4">
    <div class="bg-white rounded-2xl p-8 shadow-2xl w-full max-w-3xl">
        <div class="text-center mb-6">
            <h1 class="text-2xl font-bold text-gray-800 mb-2">🎨 Deep Image API Tester</h1>
            <p class="text-gray-600">Test your Deep Image API key and find the correct endpoint</p>
        </div>

        <!-- API Key Display -->
        <div class="bg-gray-50 p-4 rounded-lg mb-6">
            <h3 class="font-semibold text-gray-700 mb-2">Your API Key:</h3>
            <code class="text-sm text-purple-600 break-all">f7c22b80-3d79-11f0-b7b2-31db9926ded4</code>
        </div>

        <!-- Endpoint Configuration -->
        <div class="mb-6">
            <h3 class="font-semibold text-gray-700 mb-3">🔧 API Endpoint:</h3>
            <input 
                type="text" 
                id="apiEndpoint" 
                placeholder="Enter Deep Image API endpoint URL"
                value="https://api.deepimage.ai/v1/generate"
                class="w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500"
            >
            <div class="text-xs text-gray-500 mt-1">
                Common endpoints: deepimage.ai, deepai.org, or your custom Deep Image service
            </div>
        </div>

        <!-- Test Prompt -->
        <div class="mb-6">
            <h3 class="font-semibold text-gray-700 mb-3">📝 Test Prompt:</h3>
            <input 
                type="text" 
                id="testPrompt" 
                placeholder="Enter a test prompt"
                value="a cute cat sitting in a garden, digital art"
                class="w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500"
            >
        </div>

        <!-- Test Buttons -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
            <button onclick="testFormat('json-bearer')" class="test-btn bg-blue-500 hover:bg-blue-600 text-white p-4 rounded-lg transition-colors">
                <div class="font-semibold">Test JSON + Bearer Token</div>
                <div class="text-sm opacity-90">Authorization: Bearer {key}</div>
            </button>

            <button onclick="testFormat('json-apikey')" class="test-btn bg-green-500 hover:bg-green-600 text-white p-4 rounded-lg transition-colors">
                <div class="font-semibold">Test JSON + API Key Header</div>
                <div class="text-sm opacity-90">x-api-key: {key}</div>
            </button>

            <button onclick="testFormat('form-bearer')" class="test-btn bg-yellow-500 hover:bg-yellow-600 text-white p-4 rounded-lg transition-colors">
                <div class="font-semibold">Test Form Data + Bearer</div>
                <div class="text-sm opacity-90">FormData with Bearer token</div>
            </button>

            <button onclick="testFormat('form-apikey')" class="test-btn bg-purple-500 hover:bg-purple-600 text-white p-4 rounded-lg transition-colors">
                <div class="font-semibold">Test Form Data + API Key</div>
                <div class="text-sm opacity-90">FormData with x-api-key</div>
            </button>
        </div>

        <!-- Common Deep Image Services -->
        <div class="mb-6">
            <h3 class="font-semibold text-gray-700 mb-3">🌐 Try Common Deep Image Services:</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-2">
                <button onclick="setEndpoint('https://api.deepai.org/api/text2img')" class="text-left p-2 bg-gray-100 hover:bg-gray-200 rounded text-sm">
                    DeepAI Text2Image
                </button>
                <button onclick="setEndpoint('https://api.deepimage.ai/v1/generate')" class="text-left p-2 bg-gray-100 hover:bg-gray-200 rounded text-sm">
                    DeepImage.ai
                </button>
                <button onclick="setEndpoint('https://api.deepdreamgenerator.com/v1/generate')" class="text-left p-2 bg-gray-100 hover:bg-gray-200 rounded text-sm">
                    Deep Dream Generator
                </button>
                <button onclick="setEndpoint('https://api.artbreeder.com/v1/generate')" class="text-left p-2 bg-gray-100 hover:bg-gray-200 rounded text-sm">
                    Artbreeder
                </button>
            </div>
        </div>

        <!-- Results -->
        <div id="results" class="hidden">
            <h3 class="font-semibold text-gray-700 mb-3">📊 Test Results:</h3>
            <div id="resultContent" class="bg-gray-50 p-4 rounded-lg max-h-60 overflow-y-auto"></div>
        </div>

        <!-- Generated Image -->
        <div id="imageResult" class="hidden mt-6">
            <h3 class="font-semibold text-gray-700 mb-3">🖼️ Generated Image:</h3>
            <img id="generatedImage" class="w-full max-w-md mx-auto rounded-lg shadow-lg" alt="Generated image">
            <div class="text-center mt-2">
                <button onclick="copyImageUrl()" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded text-sm">
                    Copy Image URL
                </button>
            </div>
        </div>
    </div>

    <script>
        const API_KEY = 'f7c22b80-3d79-11f0-b7b2-31db9926ded4';
        let lastImageUrl = '';

        function setEndpoint(url) {
            document.getElementById('apiEndpoint').value = url;
        }

        async function testFormat(format) {
            const endpoint = document.getElementById('apiEndpoint').value.trim();
            const prompt = document.getElementById('testPrompt').value.trim();
            
            if (!endpoint) {
                alert('Please enter an API endpoint');
                return;
            }

            if (!prompt) {
                alert('Please enter a test prompt');
                return;
            }

            const resultsDiv = document.getElementById('results');
            const resultContent = document.getElementById('resultContent');
            const imageResult = document.getElementById('imageResult');
            const generatedImage = document.getElementById('generatedImage');
            
            resultsDiv.classList.remove('hidden');
            imageResult.classList.add('hidden');
            resultContent.innerHTML = `<div class="text-blue-600">🧪 Testing ${format} format...</div>`;
            
            try {
                let requestOptions;

                switch (format) {
                    case 'json-bearer':
                        requestOptions = {
                            method: 'POST',
                            headers: {
                                'Authorization': `Bearer ${API_KEY}`,
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify({
                                prompt: prompt,
                                width: 512,
                                height: 512
                            })
                        };
                        break;

                    case 'json-apikey':
                        requestOptions = {
                            method: 'POST',
                            headers: {
                                'x-api-key': API_KEY,
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify({
                                prompt: prompt,
                                width: 512,
                                height: 512
                            })
                        };
                        break;

                    case 'form-bearer':
                        const formData1 = new FormData();
                        formData1.append('text', prompt);
                        formData1.append('width', '512');
                        formData1.append('height', '512');
                        
                        requestOptions = {
                            method: 'POST',
                            headers: {
                                'Authorization': `Bearer ${API_KEY}`
                            },
                            body: formData1
                        };
                        break;

                    case 'form-apikey':
                        const formData2 = new FormData();
                        formData2.append('text', prompt);
                        formData2.append('width', '512');
                        formData2.append('height', '512');
                        
                        requestOptions = {
                            method: 'POST',
                            headers: {
                                'Api-Key': API_KEY
                            },
                            body: formData2
                        };
                        break;
                }

                console.log('Testing with options:', requestOptions);

                const response = await fetch(endpoint, requestOptions);
                
                resultContent.innerHTML = `
                    <div class="mb-2"><strong>Status:</strong> ${response.status} ${response.statusText}</div>
                    <div class="mb-2"><strong>Headers:</strong></div>
                    <div class="text-xs bg-gray-100 p-2 rounded mb-2">${Array.from(response.headers.entries()).map(([k,v]) => `${k}: ${v}`).join('<br>')}</div>
                `;

                if (!response.ok) {
                    const errorText = await response.text();
                    resultContent.innerHTML += `
                        <div class="text-red-600 font-semibold">❌ ${format} - FAILED</div>
                        <div class="text-sm text-gray-600 mt-2">Error: ${errorText}</div>
                    `;
                    return;
                }

                const contentType = response.headers.get('content-type');
                
                if (contentType && contentType.includes('application/json')) {
                    const data = await response.json();
                    console.log('Response data:', data);
                    
                    resultContent.innerHTML += `
                        <div class="text-green-600 font-semibold">✅ ${format} - SUCCESS!</div>
                        <div class="text-sm text-gray-600 mt-2">Response:</div>
                        <pre class="text-xs bg-gray-100 p-2 rounded mt-1 overflow-x-auto">${JSON.stringify(data, null, 2)}</pre>
                    `;

                    // Try to find image URL in response
                    let imageUrl = null;
                    if (data.output_url) imageUrl = data.output_url;
                    else if (data.image_url) imageUrl = data.image_url;
                    else if (data.url) imageUrl = data.url;
                    else if (data.result) imageUrl = data.result;
                    else if (data.data && data.data.url) imageUrl = data.data.url;
                    else if (data.images && data.images[0]) imageUrl = data.images[0];

                    if (imageUrl) {
                        lastImageUrl = imageUrl;
                        generatedImage.src = imageUrl;
                        imageResult.classList.remove('hidden');
                        resultContent.innerHTML += `<div class="text-green-600 mt-2">🖼️ Image URL found: ${imageUrl}</div>`;
                    }

                } else {
                    // Might be binary image data
                    const blob = await response.blob();
                    if (blob.type.startsWith('image/')) {
                        lastImageUrl = URL.createObjectURL(blob);
                        generatedImage.src = lastImageUrl;
                        imageResult.classList.remove('hidden');
                        
                        resultContent.innerHTML += `
                            <div class="text-green-600 font-semibold">✅ ${format} - SUCCESS!</div>
                            <div class="text-sm text-gray-600 mt-2">Received binary image data (${blob.type})</div>
                        `;
                    } else {
                        const text = await blob.text();
                        resultContent.innerHTML += `
                            <div class="text-yellow-600 font-semibold">⚠️ ${format} - Unexpected response</div>
                            <div class="text-sm text-gray-600 mt-2">Response: ${text}</div>
                        `;
                    }
                }

            } catch (error) {
                console.error(`${format} test failed:`, error);
                resultContent.innerHTML += `
                    <div class="text-red-600 font-semibold">❌ ${format} - ERROR</div>
                    <div class="text-sm text-gray-600 mt-2">Error: ${error.message}</div>
                `;
            }
        }

        function copyImageUrl() {
            if (lastImageUrl) {
                navigator.clipboard.writeText(lastImageUrl).then(() => {
                    alert('Image URL copied to clipboard!');
                });
            }
        }
    </script>
</body>
</html>
