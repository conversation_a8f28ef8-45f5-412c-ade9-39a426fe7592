{"name": "smokey-ai-v1", "version": "1.0.0", "description": "Smokey AI v1 - Multi-user intelligent chat assistant with authentication", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "install-deps": "npm install", "test": "echo \"No tests specified\" && exit 0"}, "keywords": ["login", "authentication", "nodejs", "express", "tailwind", "modern-ui"], "author": "Smokey AI Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "bcrypt": "^5.1.1"}, "devDependencies": {"nodemon": "^3.0.1"}, "engines": {"node": ">=14.0.0"}}