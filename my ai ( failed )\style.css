body {
    margin: 0;
    font-family: sans-serif;
    background-color: #222;
    color: #fff;
    overflow: hidden;
}

.landing-page {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    background: linear-gradient(to bottom, #333, #222);
}

.landing-page .content {
    text-align: center;
}

.landing-page h1 {
    font-size: 3em;
    margin-bottom: 0.5em;
}

.landing-page p {
    font-size: 1.2em;
    margin-bottom: 2em;
}

.landing-page button {
    padding: 1em 2em;
    font-size: 1.2em;
    background-color: rgba(255, 255, 255, 0.1);
    color: #fff;
    border: none;
    cursor: pointer;
    border-radius: 5px;
    backdrop-filter: blur(10px);
    transition: background-color 0.3s;
}

.landing-page button:hover {
    background-color: rgba(255, 255, 255, 0.2);
}

.chat-section {
    position: absolute;
    top: 100%;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: #333;
    transition: top 0.5s;
}

.chat-section.active {
    top: 0;
}

.chat-window {
    max-width: 800px;
    margin: 20px auto;
    height: 80vh;
    background-color: rgba(0, 0, 0, 0.3);
    border-radius: 10px;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.messages {
    flex-grow: 1;
    padding: 20px;
    overflow-y: scroll;
}

.message {
    padding: 10px;
    border-radius: 5px;
    margin-bottom: 10px;
}

.message.user {
    background-color: #007bff;
    color: #fff;
    align-self: flex-end;
}

.message.bot {
    background-color: #444;
    color: #fff;
    align-self: flex-start;
}

.input-area {
    padding: 20px;
    display: flex;
    background-color: rgba(0, 0, 0, 0.5);
}

.input-area input {
    flex-grow: 1;
    padding: 10px;
    border: none;
    border-radius: 5px;
    background-color: rgba(255, 255, 255, 0.1);
    color: #fff;
    margin-right: 10px;
}

.input-area button {
    padding: 10px 20px;
    border: none;
    border-radius: 5px;
    background-color: #007bff;
    color: #fff;
    cursor: pointer;
}

.welcome-message {
    text-align: center;
    padding: 10px;
    font-style: italic;
    color: #aaa;
}

body.light-mode {
    background-color: #f0f0f0;
    color: #333;
}

body.light-mode .landing-page {
    background: linear-gradient(to bottom, #eee, #f0f0f0);
}

body.light-mode .chat-section {
    background-color: #ddd;
}

body.light-mode .chat-window {
    background-color: rgba(255, 255, 255, 0.8);
}

body.light-mode .message.user {
    background-color: #00aaff;
    color: #fff;
}

body.light-mode .message.bot {
    background-color: #eee;
    color: #333;
}

body.light-mode .input-area {
    background-color: rgba(255, 255, 255, 0.5);
}

body.light-mode .input-area input {
    background-color: rgba(255, 255, 255, 0.5);
    color: #333;
}

@media (max-width: 600px) {
    .landing-page h1 {
        font-size: 2em;
    }

    .landing-page p {
        font-size: 1em;
    }

    .landing-page button {
        padding: 0.8em 1.5em;
        font-size: 1em;
    }

    .chat-window {
        margin: 10px auto;
        height: 90vh;
    }

    .input-area {
        padding: 10px;
    }

    .input-area input {
        padding: 8px;
        margin-right: 5px;
    }

    .input-area button {
        padding: 8px 15px;
    }
}
