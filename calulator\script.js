const resultInput = document.getElementById('result');
const buttons = document.querySelectorAll('.buttons button');
const darkModeToggle = document.getElementById('dark-mode');
const body = document.body;

let currentInput = '';

buttons.forEach(button => {
    button.addEventListener('click', () => {
        const buttonValue = button.getAttribute('data-value');

        if (buttonValue === 'AC') {
            currentInput = '';
            resultInput.value = '';
        } else if (buttonValue === 'C') {
            currentInput = currentInput.slice(0, -1);
            resultInput.value = currentInput;
        } else if (buttonValue === '=') {
            try {
                resultInput.value = eval(currentInput);
                currentInput = resultInput.value;
            } catch (error) {
                resultInput.value = 'Error';
            }
        } else {
            currentInput += buttonValue;
            resultInput.value = currentInput;
        }
    });
});

document.addEventListener('keydown', (event) => {
    const key = event.key;
    console.log(key);

    if (/[0-9\+\-\*\/\.=]|Enter|Escape|Backspace/.test(key)) {
        if (key === 'Enter') {
            event.preventDefault();
            document.querySelector('.equals').click();
        } else if (key === 'Escape') {
            event.preventDefault();
            document.querySelector('[data-value="AC"]').click();
        } else if (key === 'Backspace') {
            event.preventDefault();
            document.querySelector('[data-value="C"]').click();
        } else {
            event.preventDefault();
            const button = document.querySelector(".buttons button[data-value='" + key + "']");
            if (button) {
                button.dispatchEvent(new Event('click'));
            }
        }
    }
});

darkModeToggle.addEventListener('change', () => {
    body.classList.toggle('dark-mode');
});
