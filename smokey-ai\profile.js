class ProfileManager {
    constructor() {
        this.currentUser = this.getCurrentUser();
        this.originalData = null;
        this.init();
    }

    init() {
        if (!this.currentUser) {
            this.redirectToLogin();
            return;
        }

        this.loadUserProfile();
        this.setupEventListeners();
        this.updateStats();
    }

    getCurrentUser() {
        try {
            // Check session storage first (temporary login)
            let sessionData = sessionStorage.getItem('currentSession');
            if (sessionData) {
                return JSON.parse(sessionData);
            }
            
            // Check local storage (remember me)
            sessionData = localStorage.getItem('currentSession');
            if (sessionData) {
                return JSON.parse(sessionData);
            }
            
            return null;
        } catch (error) {
            console.error('Error getting current user:', error);
            return null;
        }
    }

    redirectToLogin() {
        console.log('No user session found, redirecting to login...');
        window.location.href = 'login.html';
    }

    loadUserProfile() {
        try {
            // Load user data from localStorage
            const userData = this.loadCurrentUserData();
            if (!userData) {
                console.error('User data not found');
                this.redirectToLogin();
                return;
            }

            // Load main user info from users.json
            const usersData = JSON.parse(localStorage.getItem('users') || '{"users":[]}');
            const userAccount = usersData.users.find(u => 
                u.username.toLowerCase() === this.currentUser.username.toLowerCase()
            );

            if (!userAccount) {
                console.error('User account not found');
                this.redirectToLogin();
                return;
            }

            // Store original data for reset functionality
            this.originalData = {
                username: userAccount.username,
                email: userAccount.email,
                avatar: userData.avatar || null,
                settings: userData.settings || { theme: 'dark', notifications: true }
            };

            // Populate form fields
            this.populateForm(userAccount, userData);

        } catch (error) {
            console.error('Error loading user profile:', error);
            this.showToast('Error loading profile data', 'error');
        }
    }

    loadCurrentUserData() {
        try {
            const userData = localStorage.getItem(`user_${this.currentUser.username}`);
            return userData ? JSON.parse(userData) : null;
        } catch (error) {
            console.error('Error loading user data:', error);
            return null;
        }
    }

    populateForm(userAccount, userData) {
        // Display elements
        document.getElementById('displayUsername').textContent = userAccount.username;
        document.getElementById('displayEmail').textContent = userAccount.email;

        // Form fields
        document.getElementById('username').value = userAccount.username;
        document.getElementById('email').value = userAccount.email;

        // Avatar
        if (userData.avatar) {
            this.displayAvatar(userData.avatar);
        }

        // Settings
        const settings = userData.settings || { theme: 'dark', notifications: true };
        document.getElementById('theme').value = settings.theme || 'dark';
        document.getElementById('notifications').value = settings.notifications !== false ? 'true' : 'false';

        console.log('Profile loaded successfully');
    }

    displayAvatar(avatarData) {
        const avatarPreview = document.getElementById('avatarPreview');
        
        if (avatarData) {
            avatarPreview.innerHTML = `<img src="${avatarData}" alt="Avatar" class="avatar-preview">`;
        } else {
            avatarPreview.innerHTML = '👤';
            avatarPreview.className = 'avatar-placeholder';
        }
    }

    updateStats() {
        try {
            const userData = this.loadCurrentUserData();
            if (!userData) return;

            // Chat count
            const chatCount = userData.savedChats ? userData.savedChats.length : 0;
            document.getElementById('chatCount').textContent = chatCount;

            // Memory count
            const memoryCount = userData.memory ? Object.keys(userData.memory).length : 0;
            document.getElementById('memoryCount').textContent = memoryCount;

            // Days since registration
            const createdDate = new Date(userData.createdAt || Date.now());
            const daysSince = Math.floor((Date.now() - createdDate.getTime()) / (1000 * 60 * 60 * 24));
            document.getElementById('daysSince').textContent = daysSince;

        } catch (error) {
            console.error('Error updating stats:', error);
        }
    }

    setupEventListeners() {
        // Form submission
        document.getElementById('profileForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.handleSave();
        });

        // Real-time validation
        document.getElementById('username').addEventListener('input', () => {
            this.validateUsername();
        });

        document.getElementById('email').addEventListener('input', () => {
            this.validateEmail();
        });

        document.getElementById('newPassword').addEventListener('input', () => {
            this.validateNewPassword();
        });
    }

    validateUsername() {
        const username = document.getElementById('username').value.trim();
        const usernameRegex = /^[a-zA-Z0-9_]+$/;

        this.clearError('username');

        if (!username) {
            this.showError('username', 'Username is required');
            return false;
        }

        if (username.length < 3) {
            this.showError('username', 'Username must be at least 3 characters');
            return false;
        }

        if (username.length > 20) {
            this.showError('username', 'Username must be less than 20 characters');
            return false;
        }

        if (!usernameRegex.test(username)) {
            this.showError('username', 'Username can only contain letters, numbers, and underscores');
            return false;
        }

        // Check if username is taken (only if different from current)
        if (username.toLowerCase() !== this.originalData.username.toLowerCase()) {
            const usersData = JSON.parse(localStorage.getItem('users') || '{"users":[]}');
            const exists = usersData.users.some(u => 
                u.username.toLowerCase() === username.toLowerCase()
            );
            
            if (exists) {
                this.showError('username', 'Username is already taken');
                return false;
            }
        }

        return true;
    }

    validateEmail() {
        const email = document.getElementById('email').value.trim();
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

        this.clearError('email');

        if (!email) {
            this.showError('email', 'Email is required');
            return false;
        }

        if (!emailRegex.test(email)) {
            this.showError('email', 'Please enter a valid email address');
            return false;
        }

        // Check if email is taken (only if different from current)
        if (email.toLowerCase() !== this.originalData.email.toLowerCase()) {
            const usersData = JSON.parse(localStorage.getItem('users') || '{"users":[]}');
            const exists = usersData.users.some(u => 
                u.email.toLowerCase() === email.toLowerCase()
            );
            
            if (exists) {
                this.showError('email', 'Email is already registered');
                return false;
            }
        }

        return true;
    }

    validateNewPassword() {
        const newPassword = document.getElementById('newPassword').value;
        const currentPassword = document.getElementById('currentPassword').value;

        this.clearError('newPassword');
        this.clearError('currentPassword');

        // If new password is provided, current password is required
        if (newPassword && !currentPassword) {
            this.showError('currentPassword', 'Current password is required to change password');
            return false;
        }

        if (newPassword && newPassword.length < 6) {
            this.showError('newPassword', 'New password must be at least 6 characters');
            return false;
        }

        return true;
    }

    async handleSave() {
        // Validate all fields
        const isUsernameValid = this.validateUsername();
        const isEmailValid = this.validateEmail();
        const isPasswordValid = this.validateNewPassword();

        if (!isUsernameValid || !isEmailValid || !isPasswordValid) {
            this.showToast('Please fix the errors above', 'error');
            return;
        }

        // Check current password if changing password
        const newPassword = document.getElementById('newPassword').value;
        const currentPassword = document.getElementById('currentPassword').value;

        if (newPassword) {
            const usersData = JSON.parse(localStorage.getItem('users') || '{"users":[]}');
            const userAccount = usersData.users.find(u => 
                u.username.toLowerCase() === this.currentUser.username.toLowerCase()
            );

            if (!userAccount || userAccount.password !== currentPassword) {
                this.showError('currentPassword', 'Current password is incorrect');
                this.showToast('Current password is incorrect', 'error');
                return;
            }
        }

        // Show loading state
        this.setLoadingState(true);

        try {
            await this.delay(1000); // Simulate save delay

            await this.saveProfile();
            
            this.showToast('Profile updated successfully!', 'success');
            
            // Reload profile data
            setTimeout(() => {
                this.loadUserProfile();
            }, 1000);

        } catch (error) {
            console.error('Save error:', error);
            this.showToast('Error saving profile. Please try again.', 'error');
        } finally {
            this.setLoadingState(false);
        }
    }

    async saveProfile() {
        const username = document.getElementById('username').value.trim();
        const email = document.getElementById('email').value.trim();
        const newPassword = document.getElementById('newPassword').value;
        const theme = document.getElementById('theme').value;
        const notifications = document.getElementById('notifications').value === 'true';

        // Load current data
        const usersData = JSON.parse(localStorage.getItem('users') || '{"users":[]}');
        const userData = this.loadCurrentUserData();

        // Find and update user account
        const userIndex = usersData.users.findIndex(u => 
            u.username.toLowerCase() === this.currentUser.username.toLowerCase()
        );

        if (userIndex === -1) {
            throw new Error('User account not found');
        }

        // Update main user account
        const oldUsername = usersData.users[userIndex].username;
        usersData.users[userIndex].username = username;
        usersData.users[userIndex].email = email;
        
        if (newPassword) {
            usersData.users[userIndex].password = newPassword;
        }

        // Update user data
        userData.username = username;
        userData.settings = {
            theme: theme,
            notifications: notifications
        };

        // Handle username change
        if (oldUsername !== username) {
            // Remove old user data file
            localStorage.removeItem(`user_${oldUsername}`);
            
            // Update session data
            const sessionData = {
                username: username,
                email: email,
                loginTime: new Date().toISOString()
            };
            
            if (localStorage.getItem('currentSession')) {
                localStorage.setItem('currentSession', JSON.stringify(sessionData));
            }
            
            if (sessionStorage.getItem('currentSession')) {
                sessionStorage.setItem('currentSession', JSON.stringify(sessionData));
            }
            
            this.currentUser.username = username;
        }

        // Save updated data
        localStorage.setItem('users', JSON.stringify(usersData));
        localStorage.setItem(`user_${username}`, JSON.stringify(userData));

        console.log('Profile saved successfully');
    }

    showError(fieldName, message) {
        const errorElement = document.getElementById(`${fieldName}Error`);
        const inputElement = document.getElementById(fieldName);

        if (errorElement) {
            errorElement.textContent = message;
            errorElement.classList.remove('hidden');
        }

        if (inputElement) {
            inputElement.classList.add('border-red-500');
        }
    }

    clearError(fieldName) {
        const errorElement = document.getElementById(`${fieldName}Error`);
        const inputElement = document.getElementById(fieldName);

        if (errorElement) {
            errorElement.classList.add('hidden');
        }

        if (inputElement) {
            inputElement.classList.remove('border-red-500');
        }
    }

    setLoadingState(loading) {
        const saveBtn = document.getElementById('saveBtn');
        const saveText = document.getElementById('saveText');
        const saveSpinner = document.getElementById('saveSpinner');

        saveBtn.disabled = loading;

        if (loading) {
            saveText.classList.add('hidden');
            saveSpinner.classList.remove('hidden');
        } else {
            saveText.classList.remove('hidden');
            saveSpinner.classList.add('hidden');
        }
    }

    showToast(message, type = 'success') {
        const toast = document.getElementById('toast');
        const toastContent = document.getElementById('toastContent');
        const toastMessage = document.getElementById('toastMessage');

        toastMessage.textContent = message;

        // Set color based on type
        if (type === 'error') {
            toastContent.className = 'bg-red-500 text-white px-6 py-3 rounded-lg shadow-lg flex items-center';
        } else {
            toastContent.className = 'bg-green-500 text-white px-6 py-3 rounded-lg shadow-lg flex items-center';
        }

        toast.classList.remove('hidden');

        // Auto-hide after 3 seconds
        setTimeout(() => {
            toast.classList.add('hidden');
        }, 3000);
    }

    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

// Global functions for HTML event handlers
function handleAvatarUpload(event) {
    const file = event.target.files[0];
    if (!file) return;

    // Validate file type
    if (!file.type.startsWith('image/')) {
        profileManager.showToast('Please select a valid image file', 'error');
        return;
    }

    // Validate file size (max 5MB)
    if (file.size > 5 * 1024 * 1024) {
        profileManager.showToast('Image size must be less than 5MB', 'error');
        return;
    }

    const reader = new FileReader();
    reader.onload = function(e) {
        const avatarData = e.target.result;

        // Update preview
        profileManager.displayAvatar(avatarData);

        // Save to user data
        const userData = profileManager.loadCurrentUserData();
        if (userData) {
            userData.avatar = avatarData;
            localStorage.setItem(`user_${profileManager.currentUser.username}`, JSON.stringify(userData));
        }

        profileManager.showToast('Avatar updated successfully!', 'success');
    };

    reader.readAsDataURL(file);
}

function removeAvatar() {
    if (!confirm('Are you sure you want to remove your avatar?')) return;

    // Reset to default
    profileManager.displayAvatar(null);

    // Remove from user data
    const userData = profileManager.loadCurrentUserData();
    if (userData) {
        delete userData.avatar;
        localStorage.setItem(`user_${profileManager.currentUser.username}`, JSON.stringify(userData));
    }

    profileManager.showToast('Avatar removed successfully!', 'success');
}

function togglePassword(fieldId) {
    const field = document.getElementById(fieldId);
    const type = field.getAttribute('type') === 'password' ? 'text' : 'password';
    field.setAttribute('type', type);
}

function resetForm() {
    if (!confirm('Are you sure you want to reset all changes?')) return;

    profileManager.loadUserProfile();
    profileManager.showToast('Form reset to original values', 'success');
}

// Initialize profile manager when page loads
let profileManager;
document.addEventListener('DOMContentLoaded', () => {
    profileManager = new ProfileManager();
});
