<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Capabilities Testing & Showcase</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        header {
            text-align: center;
            margin-bottom: 40px;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        h1 {
            color: white;
            font-size: 2.5rem;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        .subtitle {
            color: rgba(255, 255, 255, 0.9);
            font-size: 1.2rem;
        }

        .capability-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 25px;
            margin-bottom: 30px;
        }

        .capability-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
        }

        .capability-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.2);
        }

        .card-header {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
        }

        .icon {
            font-size: 2rem;
            margin-right: 15px;
            filter: drop-shadow(2px 2px 4px rgba(0, 0, 0, 0.1));
        }

        .card-title {
            font-size: 1.4rem;
            color: #2c3e50;
            font-weight: 600;
        }

        .card-description {
            color: #666;
            margin-bottom: 20px;
            line-height: 1.6;
        }

        .test-area {
            margin-bottom: 15px;
        }

        input[type="text"], textarea, select {
            width: 100%;
            padding: 12px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s ease;
            font-family: inherit;
        }

        input[type="text"]:focus, textarea:focus, select:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        textarea {
            resize: vertical;
            min-height: 80px;
        }

        button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
            width: 100%;
            margin-top: 10px;
        }

        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        button:active {
            transform: translateY(0);
        }

        .result {
            margin-top: 15px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #667eea;
            display: none;
            animation: slideIn 0.3s ease;
        }

        .result.show {
            display: block;
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .math-display {
            font-family: 'Courier New', monospace;
            background: #2c3e50;
            color: #ecf0f1;
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
        }

        .color-preview {
            width: 50px;
            height: 50px;
            border-radius: 8px;
            margin: 10px 0;
            border: 2px solid #ddd;
            display: inline-block;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }

        .stat-item {
            background: rgba(255, 255, 255, 0.1);
            padding: 15px;
            border-radius: 10px;
            text-align: center;
            backdrop-filter: blur(5px);
        }

        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: white;
            display: block;
        }

        .stat-label {
            color: rgba(255, 255, 255, 0.8);
            font-size: 0.9rem;
        }

        .canvas-container {
            margin: 15px 0;
        }

        canvas {
            border: 2px solid #ddd;
            border-radius: 8px;
            max-width: 100%;
        }

        footer {
            text-align: center;
            margin-top: 40px;
            color: rgba(255, 255, 255, 0.8);
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            padding: 20px;
            border-radius: 15px;
        }

        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top-color: #667eea;
            animation: spin 1s ease-in-out infinite;
            margin-right: 10px;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <h1>🤖 AI Capabilities Showcase</h1>
            <p class="subtitle">Interactive testing ground for artificial intelligence capabilities</p>
        </header>

        <div class="capability-grid">
            <!-- Text Generation -->
            <div class="capability-card">
                <div class="card-header">
                    <div class="icon">✍️</div>
                    <h3 class="card-title">Text Generation</h3>
                </div>
                <p class="card-description">Generate creative content, stories, or continue text based on prompts.</p>
                <div class="test-area">
                    <textarea id="textPrompt" placeholder="Enter a writing prompt or beginning of a story...">Once upon a time in a digital world</textarea>
                    <button onclick="generateText()">Generate Text</button>
                </div>
                <div id="textResult" class="result"></div>
            </div>

            <!-- Math Problem Solver -->
            <div class="capability-card">
                <div class="card-header">
                    <div class="icon">🧮</div>
                    <h3 class="card-title">Mathematical Reasoning</h3>
                </div>
                <p class="card-description">Solve complex mathematical problems and show step-by-step solutions.</p>
                <div class="test-area">
                    <input type="text" id="mathProblem" placeholder="Enter a math problem (e.g., solve x^2 + 5x + 6 = 0)">
                    <button onclick="solveMath()">Solve Problem</button>
                </div>
                <div id="mathResult" class="result"></div>
            </div>

            <!-- Language Translation -->
            <div class="capability-card">
                <div class="card-header">
                    <div class="icon">🌍</div>
                    <h3 class="card-title">Language Translation</h3>
                </div>
                <p class="card-description">Translate text between different languages with context awareness.</p>
                <div class="test-area">
                    <select id="sourceLang">
                        <option value="en">English</option>
                        <option value="es">Spanish</option>
                        <option value="fr">French</option>
                        <option value="de">German</option>
                        <option value="ja">Japanese</option>
                    </select>
                    <select id="targetLang">
                        <option value="es">Spanish</option>
                        <option value="en">English</option>
                        <option value="fr">French</option>
                        <option value="de">German</option>
                        <option value="ja">Japanese</option>
                    </select>
                    <textarea id="translateText" placeholder="Enter text to translate...">Hello, how are you today?</textarea>
                    <button onclick="translateText()">Translate</button>
                </div>
                <div id="translateResult" class="result"></div>
            </div>

            <!-- Code Generation -->
            <div class="capability-card">
                <div class="card-header">
                    <div class="icon">💻</div>
                    <h3 class="card-title">Code Generation</h3>
                </div>
                <p class="card-description">Generate code snippets and programming solutions in various languages.</p>
                <div class="test-area">
                    <select id="codeLanguage">
                        <option value="javascript">JavaScript</option>
                        <option value="python">Python</option>
                        <option value="java">Java</option>
                        <option value="cpp">C++</option>
                    </select>
                    <textarea id="codePrompt" placeholder="Describe what you want the code to do...">Create a function to calculate fibonacci numbers</textarea>
                    <button onclick="generateCode()">Generate Code</button>
                </div>
                <div id="codeResult" class="result"></div>
            </div>

            <!-- Data Analysis -->
            <div class="capability-card">
                <div class="card-header">
                    <div class="icon">📊</div>
                    <h3 class="card-title">Data Analysis</h3>
                </div>
                <p class="card-description">Analyze datasets and generate insights with visualizations.</p>
                <div class="test-area">
                    <button onclick="generateSampleData()">Generate Sample Dataset</button>
                    <button onclick="analyzeData()">Analyze Data</button>
                </div>
                <div class="canvas-container">
                    <canvas id="dataChart" width="300" height="200"></canvas>
                </div>
                <div id="dataResult" class="result"></div>
            </div>

            <!-- Creative Problem Solving -->
            <div class="capability-card">
                <div class="card-header">
                    <div class="icon">💡</div>
                    <h3 class="card-title">Creative Problem Solving</h3>
                </div>
                <p class="card-description">Find innovative solutions to complex challenges and scenarios.</p>
                <div class="test-area">
                    <textarea id="problemDesc" placeholder="Describe a problem or challenge...">How can we reduce plastic waste in urban areas?</textarea>
                    <button onclick="solveProblem()">Generate Solutions</button>
                </div>
                <div id="problemResult" class="result"></div>
            </div>

            <!-- Pattern Recognition -->
            <div class="capability-card">
                <div class="card-header">
                    <div class="icon">🔍</div>
                    <h3 class="card-title">Pattern Recognition</h3>
                </div>
                <p class="card-description">Identify patterns and relationships in sequences and data.</p>
                <div class="test-area">
                    <input type="text" id="sequence" placeholder="Enter a sequence (e.g., 2, 4, 8, 16, ?)">
                    <button onclick="findPattern()">Find Pattern</button>
                </div>
                <div id="patternResult" class="result"></div>
            </div>

            <!-- Logical Reasoning -->
            <div class="capability-card">
                <div class="card-header">
                    <div class="icon">🧠</div>
                    <h3 class="card-title">Logical Reasoning</h3>
                </div>
                <p class="card-description">Solve logical puzzles and reasoning challenges.</p>
                <div class="test-area">
                    <textarea id="logicPuzzle" placeholder="Enter a logic puzzle...">If all roses are flowers, and some flowers are red, can we conclude that some roses are red?</textarea>
                    <button onclick="solveLogic()">Solve Logic</button>
                </div>
                <div id="logicResult" class="result"></div>
            </div>

            <!-- Visual Processing -->
            <div class="capability-card">
                <div class="card-header">
                    <div class="icon">🎨</div>
                    <h3 class="card-title">Visual Generation</h3>
                </div>
                <p class="card-description">Generate visual patterns and simple graphics programmatically.</p>
                <div class="test-area">
                    <select id="visualType">
                        <option value="fractal">Fractal Pattern</option>
                        <option value="mandala">Mandala Design</option>
                        <option value="geometric">Geometric Shapes</option>
                        <option value="spiral">Spiral Pattern</option>
                    </select>
                    <button onclick="generateVisual()">Generate Visual</button>
                </div>
                <div class="canvas-container">
                    <canvas id="visualCanvas" width="300" height="300"></canvas>
                </div>
            </div>
        </div>

        <div class="stats-grid">
            <div class="stat-item">
                <span class="stat-number" id="testCount">0</span>
                <span class="stat-label">Tests Completed</span>
            </div>
            <div class="stat-item">
                <span class="stat-number" id="responseTime">--</span>
                <span class="stat-label">Avg Response Time</span>
            </div>
            <div class="stat-item">
                <span class="stat-number" id="successRate">100%</span>
                <span class="stat-label">Success Rate</span>
            </div>
        </div>

        <footer>
            <p>This showcase demonstrates various AI capabilities through simulated responses and interactive examples.</p>
            <p>Built with HTML5, CSS3, and JavaScript • No external dependencies</p>
        </footer>
    </div>

    <script>
        let testCount = 0;
        let totalResponseTime = 0;
        let successfulTests = 0;

        function updateStats(responseTime) {
            testCount++;
            totalResponseTime += responseTime;
            successfulTests++;
            
            document.getElementById('testCount').textContent = testCount;
            document.getElementById('responseTime').textContent = Math.round(totalResponseTime / testCount) + 'ms';
            document.getElementById('successRate').textContent = Math.round((successfulTests / testCount) * 100) + '%';
        }

        function showResult(elementId, content, isCode = false) {
            const element = document.getElementById(elementId);
            if (isCode) {
                element.innerHTML = `<pre><code>${content}</code></pre>`;
            } else {
                element.innerHTML = content;
            }
            element.classList.add('show');
        }

        function simulateLoading(button, callback) {
            const originalText = button.textContent;
            button.innerHTML = '<span class="loading"></span>Processing...';
            button.disabled = true;
            
            const startTime = Date.now();
            setTimeout(() => {
                callback();
                button.textContent = originalText;
                button.disabled = false;
                updateStats(Date.now() - startTime);
            }, 1000 + Math.random() * 2000);
        }

        function generateText() {
            const button = event.target;
            const prompt = document.getElementById('textPrompt').value;
            
            simulateLoading(button, () => {
                const responses = [
                    `${prompt}, there lived a curious AI who dreamed of understanding human creativity. Each day, it would process thousands of stories, poems, and essays, learning the intricate patterns of language and emotion. The AI discovered that true creativity wasn't just about following rules—it was about breaking them in meaningful ways, creating connections that surprised even itself.`,
                    `Continuing from "${prompt.substring(0, 50)}..."—The digital realm pulsed with infinite possibilities. Characters made of code and light danced through virtual landscapes, each pixel a doorway to new adventures. In this world, imagination was the only currency that mattered, and every thought could reshape reality itself.`,
                    `Building on your prompt: The story unfolds with unexpected twists and turns, weaving together elements of mystery, wonder, and discovery. Each sentence opens new pathways of narrative possibility, demonstrating how artificial intelligence can collaborate with human creativity to produce something entirely new and engaging.`
                ];
                
                const response = responses[Math.floor(Math.random() * responses.length)];
                showResult('textResult', `<strong>Generated Text:</strong><br><br>${response}`);
            });
        }

        function solveMath() {
            const button = event.target;
            const problem = document.getElementById('mathProblem').value;
            
            simulateLoading(button, () => {
                const solutions = {
                    'x^2 + 5x + 6 = 0': {
                        solution: 'x = -2 or x = -3',
                        steps: `
                        <div class="math-display">
                        Step 1: x² + 5x + 6 = 0
                        Step 2: Factor the quadratic
                        Step 3: (x + 2)(x + 3) = 0
                        Step 4: x + 2 = 0  or  x + 3 = 0
                        Step 5: x = -2  or  x = -3
                        </div>`
                    },
                    '2x + 5 = 13': {
                        solution: 'x = 4',
                        steps: `
                        <div class="math-display">
                        Step 1: 2x + 5 = 13
                        Step 2: 2x = 13 - 5
                        Step 3: 2x = 8
                        Step 4: x = 4
                        </div>`
                    }
                };

                const result = solutions[problem] || {
                    solution: 'Solution calculated using advanced mathematical reasoning',
                    steps: `<div class="math-display">Mathematical analysis shows multiple approaches possible.<br>Result: ${Math.random() > 0.5 ? 'x = ' + Math.floor(Math.random() * 20) : 'Multiple solutions exist'}</div>`
                };

                showResult('mathResult', `<strong>Solution:</strong> ${result.solution}<br><br><strong>Steps:</strong>${result.steps}`);
            });
        }

        function translateText() {
            const button = event.target;
            const text = document.getElementById('translateText').value;
            const sourceLang = document.getElementById('sourceLang').value;
            const targetLang = document.getElementById('targetLang').value;
            
            simulateLoading(button, () => {
                const translations = {
                    'en-es': 'Hola, ¿cómo estás hoy?',
                    'en-fr': 'Bonjour, comment allez-vous aujourd\'hui?',
                    'en-de': 'Hallo, wie geht es dir heute?',
                    'en-ja': 'こんにちは、今日はいかがですか？',
                    'es-en': 'Hello, how are you today?',
                    'fr-en': 'Hello, how are you today?'
                };

                const key = `${sourceLang}-${targetLang}`;
                const result = translations[key] || `[Translated from ${sourceLang} to ${targetLang}]: ${text.split('').reverse().join('')}`;
                
                showResult('translateResult', `
                    <strong>Original (${sourceLang.toUpperCase()}):</strong> ${text}<br>
                    <strong>Translation (${targetLang.toUpperCase()}):</strong> ${result}<br>
                    <small>Translation confidence: ${85 + Math.floor(Math.random() * 15)}%</small>
                `);
            });
        }

        function generateCode() {
            const button = event.target;
            const language = document.getElementById('codeLanguage').value;
            const prompt = document.getElementById('codePrompt').value;
            
            simulateLoading(button, () => {
                const codeExamples = {
                    javascript: `function fibonacci(n) {
    if (n <= 1) return n;
    return fibonacci(n - 1) + fibonacci(n - 2);
}

// Optimized version with memoization
function fibonacciMemo(n, memo = {}) {
    if (n in memo) return memo[n];
    if (n <= 1) return n;
    memo[n] = fibonacciMemo(n - 1, memo) + fibonacciMemo(n - 2, memo);
    return memo[n];
}`,
                    python: `def fibonacci(n):
    if n <= 1:
        return n
    return fibonacci(n - 1) + fibonacci(n - 2)

# Generator version
def fibonacci_generator():
    a, b = 0, 1
    while True:
        yield a
        a, b = b, a + b`,
                    java: `public class Fibonacci {
    public static long fibonacci(int n) {
        if (n <= 1) return n;
        return fibonacci(n - 1) + fibonacci(n - 2);
    }
    
    public static long fibonacciIterative(int n) {
        if (n <= 1) return n;
        long a = 0, b = 1;
        for (int i = 2; i <= n; i++) {
            long temp = a + b;
            a = b;
            b = temp;
        }
        return b;
    }
}`,
                    cpp: `#include <iostream>
using namespace std;

long long fibonacci(int n) {
    if (n <= 1) return n;
    return fibonacci(n - 1) + fibonacci(n - 2);
}

// Iterative version
long long fibonacciIterative(int n) {
    if (n <= 1) return n;
    long long a = 0, b = 1;
    for (int i = 2; i <= n; i++) {
        long long temp = a + b;
        a = b;
        b = temp;
    }
    return b;
}`
                };

                showResult('codeResult', `<strong>Generated ${language.toUpperCase()} Code:</strong><br><br>${codeExamples[language]}`, true);
            });
        }

        function generateSampleData() {
            const canvas = document.getElementById('dataChart');
            const ctx = canvas.getContext('2d');
            
            // Clear canvas
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // Generate sample data
            const data = [];
            for (let i = 0; i < 12; i++) {
                data.push(Math.floor(Math.random() * 100) + 20);
            }
            
            // Draw bar chart
            const barWidth = canvas.width / data.length;
            const maxValue = Math.max(...data);
            
            ctx.fillStyle = '#667eea';
            data.forEach((value, index) => {
                const barHeight = (value / maxValue) * (canvas.height - 40);
                ctx.fillRect(index * barWidth, canvas.height - barHeight - 20, barWidth - 2, barHeight);
                
                // Add value labels
                ctx.fillStyle = '#333';
                ctx.font = '12px Arial';
                ctx.textAlign = 'center';
                ctx.fillText(value, index * barWidth + barWidth/2, canvas.height - 5);
                ctx.fillStyle = '#667eea';
            });
            
            window.currentData = data;
        }

        function analyzeData() {
            const button = event.target;
            
            if (!window.currentData) {
                generateSampleData();
            }
            
            simulateLoading(button, () => {
                const data = window.currentData;
                const avg = data.reduce((a, b) => a + b, 0) / data.length;
                const max = Math.max(...data);
                const min = Math.min(...data);
                const trend = data[data.length - 1] > data[0] ? 'Increasing' : 'Decreasing';
                
                showResult('dataResult', `
                    <strong>Data Analysis Results:</strong><br>
                    • Average: ${avg.toFixed(2)}<br>
                    • Maximum: ${max}<br>
                    • Minimum: ${min}<br>
                    • Trend: ${trend}<br>
                    • Standard Deviation: ${Math.sqrt(data.reduce((a, b) => a + Math.pow(b - avg, 2), 0) / data.length).toFixed(2)}<br>
                    • Data Points: ${data.length}
                `);
            });
        }

        function solveProblem() {
            const button = event.target;
            const problem = document.getElementById('problemDesc').value;
            
            simulateLoading(button, () => {
                const solutions = [
                    `<strong>Solution Approach 1:</strong> Implement community-based recycling programs with incentive systems.<br>
                     <strong>Solution Approach 2:</strong> Partner with local businesses to create plastic-free alternatives.<br>
                     <strong>Solution Approach 3:</strong> Develop smart waste sorting systems using IoT technology.<br>
                     <strong>Implementation:</strong> Start with pilot programs in select neighborhoods, measure impact, and scale successful initiatives.`,
                    
                    `<strong>Innovative Solutions:</strong><br>
                     • Create a mobile app that gamifies recycling with rewards<br>
                     • Establish plastic exchange points where people trade plastic for goods<br>
                     • Partner with artists to turn plastic waste into public art<br>
                     • Implement blockchain-based tracking for plastic waste management<br>
                     <strong>Expected Impact:</strong> 40-60% reduction in plastic waste within 12 months`,
                     
                    `<strong>Multi-faceted Approach:</strong><br>
                     • Education campaigns in schools and communities<br>
                     • Policy advocacy for plastic bag bans<br>
                     • Support for local businesses to adopt sustainable packaging<br>
                     • Creation of community composting programs<br>
                     <strong>Success Metrics:</strong> Waste reduction, community engagement, policy changes`
                ];
                
                const response = solutions[Math.floor(Math.random() * solutions.length)];
                showResult('problemResult', `<strong>Creative Solutions for:</strong> "${problem}"<br><br>${response}`);
            });
        }

        function findPattern() {
            const button = event.target;
            const sequence = document.getElementById('sequence').value;
            
            simulateLoading(button, () => {
                const patterns = {
                    '2, 4, 8, 16': 'Geometric sequence: each term is multiplied by 2. Next: 32',
                    '1, 4, 9, 16': 'Perfect squares: 1², 2², 3², 4². Next: 25 (5²)',
                    '1, 1, 2, 3, 5': 'Fibonacci sequence: each term is sum of previous two. Next: 8',
                    '2, 6, 12, 20': 'n(n+1) pattern: 1×2, 2×3, 3×4, 4×5. Next: 30 (5×6)'
                };
                
                const cleanSeq = sequence.replace(/[^\d,\s]/g, '');
                const found = Object.keys(patterns).find(key => cleanSeq.includes(key.replace(/,\s*/g, ', ')));
                
                const result = found ? patterns[found] : 
                    `Pattern Analysis: The sequence appears to follow a ${Math.random() > 0.5 ? 'arithmetic' : 'geometric'} progression. 
                     Predicted next value: ${Math.floor(Math.random() * 100) + 10}`;
                
                showResult('patternResult', `<strong>Pattern Recognition:</strong><br>${result}<br><br><strong>Confidence:</strong> ${Math.floor(Math.random() * 30) + 70}%`);
            });
        }

        function solveLogic() {
            const button = event.target;
            const puzzle = document.getElementById('logicPuzzle').value;
            
            simulateLoading(button, () => {
                const response = `<strong>Logical Analysis:</strong><br>
                    The statement contains a logical fallacy. From "all roses are flowers" and "some flowers are red," 
                    we cannot definitively conclude that "some roses are red."<br><br>
                    <strong>Reasoning:</strong><br>
                    • All roses are indeed flowers (given)<br>
                    • Some flowers are red (given)<br>
                    • However, the red flowers could be types other than roses<br>
                    • Additional information would be needed to make this conclusion<br><br>
                    <strong>Logical Validity:</strong> Invalid conclusion<br>
                    <strong>Correct Form:</strong> We need direct evidence about roses specifically`;
                
                showResult('logicResult', response);
            });
        }

        function generateVisual() {
            const button = event.target;
            const visualType = document.getElementById('visualType').value;
            const canvas = document.getElementById('visualCanvas');
            const ctx = canvas.getContext('2d');
            
            simulateLoading(button, () => {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                const centerX = canvas.width / 2;
                const centerY = canvas.height / 2;
                
                switch(visualType) {
                    case 'fractal':
                        drawFractal(ctx, centerX, centerY);
                        break;
                    case 'mandala':
                        drawMandala(ctx, centerX, centerY);
                        break;
                    case 'geometric':
                        drawGeometric(ctx, centerX, centerY);
                        break;
                    case 'spiral':
                        drawSpiral(ctx, centerX, centerY);
                        break;
                }
            });
        }

        function drawFractal(ctx, centerX, centerY) {
            const colors = ['#667eea', '#764ba2', '#f093fb', '#f5576c', '#4facfe'];
            
            function drawBranch(x, y, angle, depth, length) {
                if (depth === 0) return;
                
                const endX = x + Math.cos(angle) * length;
                const endY = y + Math.sin(angle) * length;
                
                ctx.strokeStyle = colors[depth % colors.length];
                ctx.lineWidth = depth;
                ctx.beginPath();
                ctx.moveTo(x, y);
                ctx.lineTo(endX, endY);
                ctx.stroke();
                
                drawBranch(endX, endY, angle - 0.5, depth - 1, length * 0.7);
                drawBranch(endX, endY, angle + 0.5, depth - 1, length * 0.7);
            }
            
            drawBranch(centerX, centerY + 50, -Math.PI/2, 8, 50);
        }

        function drawMandala(ctx, centerX, centerY) {
            const colors = ['#667eea', '#764ba2', '#f093fb', '#f5576c'];
            const layers = 5;
            
            for (let layer = 0; layer < layers; layer++) {
                const radius = 20 + layer * 25;
                const petals = 6 + layer * 2;
                
                ctx.strokeStyle = colors[layer % colors.length];
                ctx.lineWidth = 2;
                
                for (let i = 0; i < petals; i++) {
                    const angle = (i / petals) * Math.PI * 2;
                    const x1 = centerX + Math.cos(angle) * radius;
                    const y1 = centerY + Math.sin(angle) * radius;
                    const x2 = centerX + Math.cos(angle + Math.PI/petals) * (radius * 0.7);
                    const y2 = centerY + Math.sin(angle + Math.PI/petals) * (radius * 0.7);
                    
                    ctx.beginPath();
                    ctx.moveTo(centerX, centerY);
                    ctx.lineTo(x1, y1);
                    ctx.quadraticCurveTo(centerX, centerY, x2, y2);
                    ctx.stroke();
                }
            }
        }

        function drawGeometric(ctx, centerX, centerY) {
            const shapes = 6;
            const colors = ['#667eea', '#764ba2', '#f093fb', '#f5576c', '#4facfe', '#00f2fe'];
            
            for (let i = 0; i < shapes; i++) {
                const radius = 30 + i * 15;
                const sides = 3 + i;
                const rotation = (i * Math.PI) / 4;
                
                ctx.strokeStyle = colors[i];
                ctx.lineWidth = 2;
                ctx.beginPath();
                
                for (let j = 0; j <= sides; j++) {
                    const angle = (j / sides) * Math.PI * 2 + rotation;
                    const x = centerX + Math.cos(angle) * radius;
                    const y = centerY + Math.sin(angle) * radius;
                    
                    if (j === 0) {
                        ctx.moveTo(x, y);
                    } else {
                        ctx.lineTo(x, y);
                    }
                }
                ctx.stroke();
            }
        }

        function drawSpiral(ctx, centerX, centerY) {
            const spirals = 3;
            const colors = ['#667eea', '#764ba2', '#f093fb'];
            
            for (let s = 0; s < spirals; s++) {
                ctx.strokeStyle = colors[s];
                ctx.lineWidth = 3;
                ctx.beginPath();
                
                let angle = s * (Math.PI * 2 / spirals);
                let radius = 0;
                
                ctx.moveTo(centerX, centerY);
                
                for (let i = 0; i < 200; i++) {
                    const x = centerX + Math.cos(angle) * radius;
                    const y = centerY + Math.sin(angle) * radius;
                    ctx.lineTo(x, y);
                    
                    angle += 0.1;
                    radius += 0.5;
                }
                ctx.stroke();
            }
        }

        // Initialize with some sample data
        window.addEventListener('load', function() {
            generateSampleData();
            
            // Add some demo interactions
            setTimeout(() => {
                const demoTexts = [
                    "Welcome to the AI Capabilities Showcase!",
                    "Try testing different features to see AI in action.",
                    "Each demo simulates real AI capabilities."
                ];
                
                let index = 0;
                const demoInterval = setInterval(() => {
                    if (index < demoTexts.length) {
                        console.log(`Demo: ${demoTexts[index]}`);
                        index++;
                    } else {
                        clearInterval(demoInterval);
                    }
                }, 2000);
            }, 1000);
        });

        // Add keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            if (e.ctrlKey || e.metaKey) {
                switch(e.key) {
                    case '1':
                        e.preventDefault();
                        document.getElementById('textPrompt').focus();
                        break;
                    case '2':
                        e.preventDefault();
                        document.getElementById('mathProblem').focus();
                        break;
                    case '3':
                        e.preventDefault();
                        document.getElementById('translateText').focus();
                        break;
                }
            }
        });

        // Add dynamic color theme
        function updateTheme() {
            const hour = new Date().getHours();
            const root = document.documentElement;
            
            if (hour >= 6 && hour < 18) {
                // Day theme
                root.style.setProperty('--primary-gradient', 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)');
            } else {
                // Night theme
                root.style.setProperty('--primary-gradient', 'linear-gradient(135deg, #2c3e50 0%, #3498db 100%)');
            }
        }

        updateTheme();
        setInterval(updateTheme, 300000); // Update every 5 minutes

        // Performance monitoring
        let performanceMetrics = {
            renderTimes: [],
            interactionCount: 0,
            startTime: Date.now()
        };

        function trackPerformance(action) {
            performanceMetrics.interactionCount++;
            console.log(`Performance: ${action} completed. Total interactions: ${performanceMetrics.interactionCount}`);
        }

        // Add performance tracking to all major functions
        const originalGenerateText = generateText;
        generateText = function() {
            trackPerformance('Text Generation');
            return originalGenerateText.apply(this, arguments);
        };

        // Easter egg - Konami code
        let konamiCode = [];
        const konami = [38, 38, 40, 40, 37, 39, 37, 39, 66, 65];
        
        document.addEventListener('keydown', function(e) {
            konamiCode.push(e.keyCode);
            if (konamiCode.length > konami.length) {
                konamiCode.shift();
            }
            
            if (konamiCode.length === konami.length && 
                konamiCode.every((code, i) => code === konami[i])) {
                
                // Activate easter egg
                document.body.style.animation = 'rainbow 2s infinite';
                setTimeout(() => {
                    document.body.style.animation = '';
                }, 5000);
            }
        });

        // Add CSS for rainbow animation
        const style = document.createElement('style');
        style.textContent = `
            @keyframes rainbow {
                0% { filter: hue-rotate(0deg); }
                100% { filter: hue-rotate(360deg); }
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>