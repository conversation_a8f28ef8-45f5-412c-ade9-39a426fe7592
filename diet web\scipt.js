// script.js
// Indian food database with calories per portion
const INDIAN_FOOD_DB = {
    "Roti": { calories: 70, carbs: 15, protein: 3, fat: 0.4 },
    "Dal Tadka": { calories: 150, carbs: 22, protein: 8, fat: 4 },
    "Paneer Tikka": { calories: 250, carbs: 8, protein: 14, fat: 18 },
    "Chicken Biryani": { calories: 400, carbs: 60, protein: 20, fat: 10 },
    "Masala Dosa": { calories: 200, carbs: 30, protein: 5, fat: 7 },
    "Idli (2 pieces)": { calories: 120, carbs: 24, protein: 4, fat: 1 },
    "Samosa": { calories: 150, carbs: 18, protein: 3, fat: 8 },
    "Butter Chicken": { calories: 350, carbs: 10, protein: 25, fat: 22 },
    "Rajma <PERSON>": { calories: 300, carbs: 45, protein: 12, fat: 8 },
    "Palak Paneer": { calories: 280, carbs: 12, protein: 14, fat: 20 },
    "<PERSON>oo Paratha": { calories: 200, carbs: 30, protein: 6, fat: 7 },
    "Chole Bhature": { calories: 450, carbs: 60, protein: 12, fat: 18 },
    "Vada Pav": { calories: 250, carbs: 35, protein: 6, fat: 10 },
    "Poha": { calories: 150, carbs: 30, protein: 4, fat: 3 },
    "Upma": { calories: 180, carbs: 28, protein: 5, fat: 5 }
};

// DOM Elements
const themeToggle = document.getElementById('themeToggle');
const addMealBtn = document.getElementById('addMealBtn');
const closeModal = document.getElementById('closeModal');
const mealForm = document.getElementById('mealForm');
const mealNameInput = document.getElementById('mealName');
const portionSizeSelect = document.getElementById('portionSize');
const mealCaloriesInput = document.getElementById('mealCalories');
const mealList = document.getElementById('mealList');
const dailyCaloriesSpan = document.getElementById('dailyCalories');
const targetCaloriesSpan = document.getElementById('targetCalories');
const caloriePercentageSpan = document.getElementById('caloriePercentage');
const modal = document.getElementById('addMealModal');
const autocompleteResults = document.querySelector('.autocomplete-results');

// Chart instances
const calorieChart = new Chart(
    document.getElementById('calorieChart'),
    {
        type: 'doughnut',
        data: {
            labels: ['Consumed', 'Remaining'],
            datasets: [{
                data: [0, 2000],
                backgroundColor: ['#7b2cbf', '#1e1e1e'],
                borderWidth: 0
            }]
        },
        options: {
            cutout: '70%',
            responsive: true,
            plugins: {
                legend: { display: false },
                tooltip: { enabled: false }
            },
            animation: {
                animateScale: true,
                animateRotate: true
            }
        }
    }
);

const nutrientChart = new Chart(
    document.getElementById('nutrientChart'),
    {
        type: 'pie',
        data: {
            labels: ['Carbs', 'Protein', 'Fat'],
            datasets: [{
                data: [0, 0, 0],
                backgroundColor: ['#4cc9f0', '#f8961e', '#f94144'],
                borderWidth: 0
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: { position: 'bottom' }
            }
        }
    }
);

// App state
let meals = JSON.parse(localStorage.getItem('meals')) || [];
let targetCalories = 2000;

// Initialize the app
function init() {
    // Initialize Lucide icons
    lucide.createIcons();
    
    // Set target calories
    targetCaloriesSpan.textContent = targetCalories;
    
    // Load meals from localStorage
    renderMeals();
    updateCharts();
    
    // Set up event listeners
    setupEventListeners();
}

// Set up event listeners
function setupEventListeners() {
    // Theme toggle
    themeToggle.addEventListener('click', toggleTheme);
    
    // Modal controls
    addMealBtn.addEventListener('click', openModal);
    closeModal.addEventListener('click', closeModal);
    
    // Meal form submission
    mealForm.addEventListener('submit', handleMealSubmit);
    
    // Autocomplete functionality
    mealNameInput.addEventListener('input', handleMealSearch);
    
    // Close modal when clicking outside
    modal.addEventListener('click', (e) => {
        if (e.target === modal) closeModal();
    });
}

// Theme management
function toggleTheme() {
    const currentTheme = document.body.getAttribute('data-theme');
    const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
    document.body.setAttribute('data-theme', newTheme);
    localStorage.setItem('theme', newTheme);
    
    // Update icon
    const icon = themeToggle.querySelector('i');
    icon.setAttribute('data-lucide', newTheme === 'dark' ? 'moon' : 'sun');
    lucide.createIcons();
}

// Modal controls
function openModal() {
    modal.style.display = 'flex';
    mealNameInput.focus();
}

function closeModal() {
    modal.style.display = 'none';
    mealForm.reset();
    autocompleteResults.style.display = 'none';
}

// Meal search and autocomplete
function handleMealSearch() {
    const searchTerm = mealNameInput.value.toLowerCase();
    
    if (searchTerm.length < 2) {
        autocompleteResults.style.display = 'none';
        return;
    }
    
    const matches = Object.keys(INDIAN_FOOD_DB).filter(food => 
        food.toLowerCase().includes(searchTerm)
    );
    
    if (matches.length > 0) {
        autocompleteResults.innerHTML = matches.map(food => `
            <div class="autocomplete-item" data-food="${food}">
                ${food} <small>${INDIAN_FOOD_DB[food].calories} kcal</small>
            </div>
        `).join('');
        
        autocompleteResults.style.display = 'block';
        
        // Add click event to autocomplete items
        document.querySelectorAll('.autocomplete-item').forEach(item => {
            item.addEventListener('click', () => {
                const foodName = item.getAttribute('data-food');
                const foodData = INDIAN_FOOD_DB[foodName];
                
                mealNameInput.value = foodName;
                mealCaloriesInput.value = foodData.calories;
                autocompleteResults.style.display = 'none';
            });
        });
    } else {
        autocompleteResults.style.display = 'none';
    }
}

// Handle meal form submission
function handleMealSubmit(e) {
    e.preventDefault();
    
    const mealName = mealNameInput.value.trim();
    const portionSize = parseFloat(portionSizeSelect.value);
    let calories = parseInt(mealCaloriesInput.value);
    
    if (!mealName || isNaN(calories)) {
        alert('Please enter a valid meal name and calories');
        return;
    }
    
    // Adjust calories based on portion size
    calories = Math.round(calories * portionSize);
    
    // Create new meal object
    const newMeal = {
        id: Date.now(),
        name: mealName,
        calories: calories,
        time: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
    };
    
    // Add to meals array
    meals.push(newMeal);
    
    // Save to localStorage
    saveMeals();
    
    // Update UI
    renderMeals();
    updateCharts();
    
    // Close modal and reset form
    closeModal();
}

// Render meals list
function renderMeals() {
    if (meals.length === 0) {
        mealList.innerHTML = `
            <div class="empty-state">
                <i data-lucide="utensils-crossed" width="48" height="48"></i>
                <p>No meals logged yet</p>
                <button class="primary-btn" id="addFirstMeal">
                    <i data-lucide="plus"></i> Add Your First Meal
                </button>
            </div>
        `;
        lucide.createIcons();
        
        document.getElementById('addFirstMeal')?.addEventListener('click', openModal);
        return;
    }
    
    mealList.innerHTML = meals.map(meal => `
        <div class="meal-item" data-id="${meal.id}">
            <div class="meal-info">
                <span class="meal-name">${meal.name}</span>
                <span class="meal-calories">${meal.calories} kcal • ${meal.time}</span>
            </div>
            <div class="meal-actions">
                <button class="icon-btn delete-meal" aria-label="Delete meal">
                    <i data-lucide="trash-2"></i>
                </button>
            </div>
        </div>
    `).join('');
    
    lucide.createIcons();
    
    // Add delete event listeners
    document.querySelectorAll('.delete-meal').forEach(btn => {
        btn.addEventListener('click', (e) => {
            const mealId = parseInt(btn.closest('.meal-item').getAttribute('data-id'));
            deleteMeal(mealId);
        });
    });
}

// Delete a meal
function deleteMeal(id) {
    meals = meals.filter(meal => meal.id !== id);
    saveMeals();
    renderMeals();
    updateCharts();
}

// Update charts
function updateCharts() {
    // Calculate totals
    const totalCalories = meals.reduce((sum, meal) => sum + meal.calories, 0);
    const remainingCalories = Math.max(0, targetCalories - totalCalories);
    
    // Calculate nutrient breakdown (simplified)
    const totalCarbs = meals.reduce((sum, meal) => {
        const food = INDIAN_FOOD_DB[meal.name] || { carbs: meal.calories * 0.6 / 4 };
        return sum + (food.carbs || 0);
    }, 0);
    
    const totalProtein = meals.reduce((sum, meal) => {
        const food = INDIAN_FOOD_DB[meal.name] || { protein: meal.calories * 0.2 / 4 };
        return sum + (food.protein || 0);
    }, 0);
    
    const totalFat = meals.reduce((sum, meal) => {
        const food = INDIAN_FOOD_DB[meal.name] || { fat: meal.calories * 0.2 / 9 };
        return sum + (food.fat || 0);
    }, 0);
    
    // Update calorie chart
    calorieChart.data.datasets[0].data = [totalCalories, remainingCalories];
    
    // Change color based on percentage
    const percentage = (totalCalories / targetCalories) * 100;
    let chartColor = '#7b2cbf'; // Default purple
    
    if (percentage > 90) {
        chartColor = '#f94144'; // Red for over
    } else if (percentage > 70) {
        chartColor = '#f8961e'; // Orange for near
    } else if (percentage > 30) {
        chartColor = '#4cc9f0'; // Blue for good
    }
    
    calorieChart.data.datasets[0].backgroundColor[0] = chartColor;
    calorieChart.update();
    
    // Update nutrient chart
    nutrientChart.data.datasets[0].data = [
        Math.round(totalCarbs),
        Math.round(totalProtein),
        Math.round(totalFat)
    ];
    nutrientChart.update();
    
    // Update text displays
    dailyCaloriesSpan.textContent = totalCalories;
    caloriePercentageSpan.textContent = `${Math.round(percentage)}%`;
    
    // Update gradient text color based on percentage
    if (percentage > 100) {
        dailyCaloriesSpan.style.backgroundImage = 'linear-gradient(135deg, #f94144, #f8961e)';
    } else {
        dailyCaloriesSpan.style.backgroundImage = 'linear-gradient(135deg, var(--accent-primary), var(--accent-secondary))';
    }
}

// Save meals to localStorage
function saveMeals() {
    localStorage.setItem('meals', JSON.stringify(meals));
}

// Initialize the app when DOM is loaded
document.addEventListener('DOMContentLoaded', init);