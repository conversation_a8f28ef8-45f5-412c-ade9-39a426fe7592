/* Base Styles & Variables */
:root {
    --primary-color: #4361ee;
    --secondary-color: #3f37c9;
    --accent-color: #4cc9f0;
    --text-color: #333;
    --bg-color: #f8f9fa;
    --card-color: #fff;
    --shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s ease;
}

[data-theme="dark"] {
    --primary-color: #4cc9f0;
    --secondary-color: #4361ee;
    --accent-color: #3f37c9;
    --text-color: #f8f9fa;
    --bg-color: #121212;
    --card-color: #1e1e1e;
    --shadow: 0 4px 6px rgba(0, 0, 0, 0.3);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    color: var(--text-color);
    background-color: var(--bg-color);
    line-height: 1.6;
    transition: var(--transition);
    overflow-x: hidden;
}

/* Loading Animation */
.loader {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: var(--bg-color);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    transition: opacity 0.5s ease;
}

.loader-spinner {
    width: 50px;
    height: 50px;
    border: 5px solid rgba(76, 201, 240, 0.3);
    border-radius: 50%;
    border-top-color: var(--primary-color);
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

.loader.hidden {
    opacity: 0;
    pointer-events: none;
}

/* Theme Toggle */
.theme-toggle {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1000;
}

.toggle-label {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 60px;
    height: 30px;
    background: var(--card-color);
    border-radius: 50px;
    padding: 5px;
    position: relative;
    cursor: pointer;
    box-shadow: var(--shadow);
}

.toggle-label i {
    font-size: 14px;
    z-index: 1;
}

.fa-sun { color: #f39c12; }
.fa-moon { color: #f1c40f; }

.toggle-ball {
    position: absolute;
    top: 2px;
    left: 2px;
    width: 26px;
    height: 26px;
    background-color: var(--primary-color);
    border-radius: 50%;
    transition: transform 0.3s ease;
}

#toggle:checked + .toggle-label .toggle-ball {
    transform: translateX(30px);
}

#toggle {
    display: none;
}

/* Header Styles */
.parallax-header {
    height: 100vh;
    background: linear-gradient(135deg, var(--secondary-color), var(--primary-color));
    color: white;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.parallax-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100" preserveAspectRatio="none"><circle cx="50" cy="50" r="40" stroke="white" stroke-width="0.5" fill="none" opacity="0.1"/></svg>');
    background-size: 50px 50px;
    opacity: 0.1;
    animation: moveBackground 100s linear infinite;
}

@keyframes moveBackground {
    from { background-position: 0 0; }
    to { background-position: 1000px 1000px; }
}

nav {
    position: absolute;
    top: 0;
    width: 100%;
    padding: 20px;
}

nav ul {
    display: flex;
    justify-content: center;
    list-style: none;
}

nav ul li a {
    color: white;
    text-decoration: none;
    margin: 0 15px;
    padding: 5px 10px;
    position: relative;
    font-weight: 500;
    transition: var(--transition);
}

nav ul li a::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 2px;
    background-color: white;
    transition: width 0.3s ease;
}

nav ul li a:hover::after {
    width: 100%;
}

.title-animation {
    font-size: 4rem;
    margin-bottom: 20px;
    text-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
    animation: float 3s ease-in-out infinite;
    transition: var(--transition);
}

.title-animation:hover {
    color: var(--accent-color);
    text-shadow: 0 2px 20px rgba(255, 255, 255, 0.3);
}

@keyframes float {
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(-10px); }
}

.scroll-down {
    position: absolute;
    bottom: 30px;
    left: 50%;
    transform: translateX(-50%);
    text-align: center;
    color: white;
    opacity: 0.8;
    animation: pulse 2s infinite;
}

.mouse {
    width: 25px;
    height: 40px;
    border: 2px solid white;
    border-radius: 15px;
    display: inline-block;
    margin-bottom: 5px;
    position: relative;
}

.scroller {
    width: 3px;
    height: 8px;
    background: white;
    border-radius: 3px;
    position: absolute;
    top: 5px;
    left: 50%;
    transform: translateX(-50%);
    animation: scroll 2s infinite;
}

@keyframes scroll {
    0% { top: 5px; opacity: 1; }
    100% { top: 20px; opacity: 0; }
}

@keyframes pulse {
    0%, 100% { opacity: 0.8; }
    50% { opacity: 0.4; }
}

/* Section Styles */
section {
    padding: 80px 20px;
    position: relative;
}

h2 {
    font-size: 2.5rem;
    margin-bottom: 40px;
    text-align: center;
    position: relative;
}

h2::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 3px;
    background: var(--primary-color);
}

/* About Section */
.about-section {
    background-color: var(--bg-color);
}

.about-content {
    max-width: 800px;
    margin: 0 auto;
    text-align: center;
    opacity: 0;
    transform: translateY(20px);
    transition: opacity 0.6s ease, transform 0.6s ease;
}

.about-content.visible {
    opacity: 1;
    transform: translateY(0);
}

.tech-stack {
    display: flex;
    justify-content: center;
    margin-top: 30px;
    gap: 20px;
}

.tech-item {
    width: 50px;
    height: 50px;
    background: var(--card-color);
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 1.5rem;
    color: var(--primary-color);
    box-shadow: var(--shadow);
    position: relative;
    cursor: pointer;
    transition: var(--transition);
}

.tech-item:hover {
    transform: translateY(-5px);
    color: var(--accent-color);
}

.tech-item::after {
    content: attr(data-tooltip);
    position: absolute;
    bottom: -40px;
    left: 50%;
    transform: translateX(-50%);
    background: var(--card-color);
    color: var(--text-color);
    padding: 5px 10px;
    border-radius: 5px;
    font-size: 0.8rem;
    opacity: 0;
    visibility: hidden;
    transition: var(--transition);
    white-space: nowrap;
    box-shadow: var(--shadow);
}

.tech-item:hover::after {
    opacity: 1;
    visibility: visible;
    bottom: -35px;
}

/* Gallery Section */
.gallery-section {
    background-color: var(--card-color);
}

.animation-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 30px;
    max-width: 1200px;
    margin: 0 auto;
}

.animation-card {
    background: var(--bg-color);
    border-radius: 10px;
    overflow: hidden;
    box-shadow: var(--shadow);
    transition: var(--transition);
    padding: 20px;
    text-align: center;
}

.animation-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
}

.animation-box {
    width: 100%;
    height: 250px;
    background: var(--card-color);
    border-radius: 8px;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 15px;
    position: relative;
    overflow: hidden;
}

.animation-card h3 {
    margin-bottom: 10px;
    color: var(--primary-color);
}

.animation-card p {
    margin-bottom: 15px;
    font-size: 0.9rem;
    color: var(--text-color);
    opacity: 0.8;
}

.animation-control {
    background: var(--primary-color);
    color: white;
    border: none;
    padding: 8px 15px;
    border-radius: 5px;
    cursor: pointer;
    transition: var(--transition);
    font-weight: 500;
}

.animation-control:hover {
    background: var(--secondary-color);
    transform: scale(1.05);
}

/* Animation Elements */
.rotating-element {
    width: 80px;
    height: 80px;
    background: var(--primary-color);
    animation: rotate 4s linear infinite;
    transform-style: preserve-3d;
}

@keyframes rotate {
    from { transform: rotateX(0) rotateY(0); }
    to { transform: rotateX(360deg) rotateY(360deg); }
}

.bouncing-ball {
    width: 50px;
    height: 50px;
    background: var(--accent-color);
    border-radius: 50%;
    animation: bounce 1s ease-in-out infinite alternate;
}

@keyframes bounce {
    from { transform: translateY(0); }
    to { transform: translateY(-70px); }
}

.fading-element {
    width: 100px;
    height: 100px;
    background: var(--secondary-color);
    border-radius: 10px;
    animation: fadePulse 2s ease-in-out infinite;
}

@keyframes fadePulse {
    0%, 100% { opacity: 0.5; transform: scale(0.9); }
    50% { opacity: 1; transform: scale(1.1); }
}

.morphing-element {
    width: 100px;
    height: 100px;
    background: var(--primary-color);
    border-radius: 50%;
}

#threejs-container {
    width: 100%;
    height: 100%;
}

#particle-canvas {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
}

/* Contact Section */
.contact-section {
    background-color: var(--bg-color);
}

.contact-form {
    max-width: 600px;
    margin: 0 auto;
    padding: 30px;
    background: var(--card-color);
    border-radius: 10px;
    box-shadow: var(--shadow);
}

.form-group {
    margin-bottom: 25px;
    position: relative;
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 10px 0;
    font-size: 1rem;
    border: none;
    border-bottom: 1px solid #ccc;
    outline: none;
    background: transparent;
    color: var(--text-color);
}

.form-group textarea {
    resize: vertical;
    min-height: 100px;
}

.underline {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--primary-color);
    transition: width 0.3s ease;
}

.form-group input:focus ~ .underline,
.form-group textarea:focus ~ .underline {
    width: 100%;
}

.submit-btn {
    background: var(--primary-color);
    color: white;
    border: none;
    padding: 12px 30px;
    border-radius: 30px;
    cursor: pointer;
    font-size: 1rem;
    font-weight: 500;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
    transition: var(--transition);
    overflow: hidden;
    position: relative;
}

.submit-btn i {
    margin-left: 10px;
    transform: translateX(30px);
    opacity: 0;
    transition: var(--transition);
}

.submit-btn:hover {
    background: var(--secondary-color);
    padding-right: 20px;
    padding-left: 40px;
}

.submit-btn:hover i {
    transform: translateX(0);
    opacity: 1;
}

.social-links {
    display: flex;
    justify-content: center;
    margin-top: 30px;
    gap: 20px;
}

.social-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: var(--card-color);
    display: flex;
    justify-content: center;
    align-items: center;
    color: var(--primary-color);
    font-size: 1.2rem;
    transition: var(--transition);
    box-shadow: var(--shadow);
}

.social-icon:hover {
    color: white;
    background: var(--primary-color);
    transform: translateY(-5px);
}

/* Footer */
footer {
    background: var(--card-color);
    padding: 30px 0;
    text-align: center;
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
}

.footer-content {
    max-width: 1200px;
    margin: 0 auto;
    position: relative;
}

.spinning-globe {
    font-size: 2rem;
    color: var(--primary-color);
    margin: 20px 0;
    display: inline-block;
    animation: spin 10s linear infinite;
}

.back-to-top {
    position: fixed;
    bottom: 30px;
    right: 30px;
    width: 50px;
    height: 50px;
    background: var(--primary-color);
    color: white;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 1.2rem;
    box-shadow: var(--shadow);
    opacity: 0;
    visibility: hidden;
    transition: var(--transition);
    z-index: 99;
}

.back-to-top.visible {
    opacity: 1;
    visibility: visible;
}

.back-to-top:hover {
    background: var(--secondary-color);
    transform: translateY(-5px);
}

/* Responsive Design */
@media (max-width: 768px) {
    .title-animation {
        font-size: 2.5rem;
    }
    
    .animation-grid {
        grid-template-columns: 1fr;
    }
    
    nav ul {
        flex-wrap: wrap;
    }
    
    nav ul li {
        margin: 5px 0;
    }
}

/* Scroll Reveal Animation */
.reveal {
    position: relative;
    transform: translateY(50px);
    opacity: 0;
    transition: all 1s ease;
}

.reveal.active {
    transform: translateY(0);
    opacity: 1;
}