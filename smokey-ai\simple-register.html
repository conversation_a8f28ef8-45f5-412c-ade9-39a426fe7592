<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Registration - Smokey AI</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
    </style>
</head>
<body class="flex items-center justify-center p-4">
    <div class="bg-white rounded-2xl p-8 shadow-2xl w-full max-w-md">
        <div class="text-center mb-6">
            <h1 class="text-2xl font-bold text-gray-800 mb-2">🐾 Join Smokey AI</h1>
            <p class="text-gray-600">Create your account</p>
        </div>

        <form id="simpleForm">
            <div class="space-y-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Username</label>
                    <input 
                        type="text" 
                        id="username" 
                        placeholder="Enter username"
                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500"
                        required
                    >
                    <div id="usernameError" class="text-red-500 text-sm mt-1 hidden"></div>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Email</label>
                    <input 
                        type="email" 
                        id="email" 
                        placeholder="Enter email"
                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500"
                        required
                    >
                    <div id="emailError" class="text-red-500 text-sm mt-1 hidden"></div>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Password</label>
                    <input 
                        type="password" 
                        id="password" 
                        placeholder="Enter password"
                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500"
                        required
                    >
                    <div id="passwordError" class="text-red-500 text-sm mt-1 hidden"></div>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Confirm Password</label>
                    <input 
                        type="password" 
                        id="confirmPassword" 
                        placeholder="Confirm password"
                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500"
                        required
                    >
                    <div id="confirmPasswordError" class="text-red-500 text-sm mt-1 hidden"></div>
                </div>

                <div class="flex items-center">
                    <input type="checkbox" id="terms" class="mr-2" required>
                    <label for="terms" class="text-sm text-gray-700">I agree to the terms</label>
                </div>
                <div id="termsError" class="text-red-500 text-sm hidden"></div>

                <button 
                    type="submit" 
                    id="submitBtn"
                    class="w-full bg-purple-600 text-white py-2 px-4 rounded-lg hover:bg-purple-700 transition-colors"
                >
                    <span id="submitText">Create Account</span>
                    <span id="submitSpinner" class="hidden">Creating...</span>
                </button>
            </div>
        </form>

        <div class="mt-4 text-center">
            <a href="login.html" class="text-purple-600 hover:text-purple-700">Already have an account? Sign in</a>
        </div>

        <div id="message" class="mt-4 p-3 rounded-lg hidden"></div>
    </div>

    <script>
        console.log('Simple registration page loaded');

        document.getElementById('simpleForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            console.log('Form submitted');

            // Get form data
            const username = document.getElementById('username').value.trim();
            const email = document.getElementById('email').value.trim();
            const password = document.getElementById('password').value;
            const confirmPassword = document.getElementById('confirmPassword').value;
            const terms = document.getElementById('terms').checked;

            console.log('Form data:', { username, email, password: '***', terms });

            // Clear previous errors
            document.querySelectorAll('[id$="Error"]').forEach(el => el.classList.add('hidden'));

            // Simple validation
            let hasErrors = false;

            if (!username || username.length < 3) {
                showError('username', 'Username must be at least 3 characters');
                hasErrors = true;
            }

            if (!email || !email.includes('@')) {
                showError('email', 'Please enter a valid email');
                hasErrors = true;
            }

            if (!password || password.length < 6) {
                showError('password', 'Password must be at least 6 characters');
                hasErrors = true;
            }

            if (password !== confirmPassword) {
                showError('confirmPassword', 'Passwords do not match');
                hasErrors = true;
            }

            if (!terms) {
                showError('terms', 'You must accept the terms');
                hasErrors = true;
            }

            if (hasErrors) {
                console.log('Validation failed');
                return;
            }

            console.log('Validation passed, creating user...');

            // Show loading
            setLoading(true);

            try {
                // Get existing users
                const existingUsers = JSON.parse(localStorage.getItem('users') || '{"users":[]}');
                console.log('Existing users:', existingUsers);

                // Check for duplicates
                const userExists = existingUsers.users.some(u => 
                    u.username.toLowerCase() === username.toLowerCase() || 
                    u.email.toLowerCase() === email.toLowerCase()
                );

                if (userExists) {
                    throw new Error('Username or email already exists');
                }

                // Create new user
                const newUser = {
                    id: Date.now(),
                    username: username.toLowerCase(),
                    email: email.toLowerCase(),
                    password: password,
                    createdAt: new Date().toISOString(),
                    lastLogin: null
                };

                // Add to users list
                existingUsers.users.push(newUser);
                localStorage.setItem('users', JSON.stringify(existingUsers));

                // Create user data
                const userData = {
                    username: username.toLowerCase(),
                    memory: {},
                    savedChats: [],
                    settings: { theme: 'dark', notifications: true },
                    createdAt: new Date().toISOString(),
                    lastLogin: null
                };

                localStorage.setItem(`user_${username.toLowerCase()}`, JSON.stringify(userData));

                console.log('User created successfully!');
                showMessage('Account created successfully! Redirecting...', 'success');

                // Redirect after 2 seconds
                setTimeout(() => {
                    window.location.href = 'login.html?registered=true&username=' + encodeURIComponent(username);
                }, 2000);

            } catch (error) {
                console.error('Registration error:', error);
                showMessage(error.message, 'error');
            } finally {
                setLoading(false);
            }
        });

        function showError(field, message) {
            const errorEl = document.getElementById(field + 'Error');
            if (errorEl) {
                errorEl.textContent = message;
                errorEl.classList.remove('hidden');
            }
            console.log(`Error for ${field}: ${message}`);
        }

        function showMessage(message, type) {
            const messageEl = document.getElementById('message');
            messageEl.textContent = message;
            messageEl.className = `mt-4 p-3 rounded-lg ${type === 'success' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`;
            messageEl.classList.remove('hidden');
        }

        function setLoading(loading) {
            const btn = document.getElementById('submitBtn');
            const text = document.getElementById('submitText');
            const spinner = document.getElementById('submitSpinner');

            btn.disabled = loading;
            if (loading) {
                text.classList.add('hidden');
                spinner.classList.remove('hidden');
            } else {
                text.classList.remove('hidden');
                spinner.classList.add('hidden');
            }
        }
    </script>
</body>
</html>
