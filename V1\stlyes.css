* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

body {
    background: linear-gradient(135deg, #72edf2 0%, #5151e5 100%);
    min-height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 20px;
}

.container {
    width: 100%;
    max-width: 500px;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 16px;
    padding: 30px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

h1 {
    color: #333;
    text-align: center;
    margin-bottom: 20px;
}

.search-container {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
}

#city-input {
    flex: 1;
    padding: 12px 15px;
    border: none;
    border-radius: 8px;
    font-size: 16px;
    outline: none;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

#search-btn {
    padding: 12px 20px;
    background: #5151e5;
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-size: 16px;
    transition: background 0.3s;
}

#search-btn:hover {
    background: #3a3ac2;
}

.error {
    color: #e74c3c;
    text-align: center;
    margin-bottom: 15px;
    font-weight: 500;
}

.weather-card {
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.weather-card.hidden {
    display: none;
}

#city-name {
    color: #333;
    text-align: center;
    margin-bottom: 15px;
    font-size: 24px;
}

.weather-info {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.weather-main {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-bottom: 15px;
    border-bottom: 1px solid #eee;
}

#temperature {
    font-size: 48px;
    font-weight: bold;
    color: #333;
}

#weather-desc {
    font-size: 18px;
    color: #666;
    text-transform: capitalize;
}

.weather-details {
    display: flex;
    justify-content: space-between;
}

.weather-details div {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.weather-details span:first-child {
    color: #666;
    font-size: 14px;
}

.weather-details span:last-child {
    color: #333;
    font-weight: 500;
    font-size: 16px;
}

@media (max-width: 480px) {
    .container {
        padding: 20px;
    }
    
    .weather-main {
        flex-direction: column;
        gap: 10px;
        text-align: center;
    }
    
    .weather-details {
        flex-direction: column;
        gap: 10px;
    }
}